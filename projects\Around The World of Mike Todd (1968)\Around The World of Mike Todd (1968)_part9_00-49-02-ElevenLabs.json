{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754281703", "text": "On March 22, 1958, <PERSON> flew back to New York from the coast in his private plane, The Lucky Liz. <PERSON> had a bad case of flu and didn't go with him. The plane was caught in a heavy storm over New Mexico and crashed. For five days, his death was headline news all over the world. <PERSON> died as he lived, spectacularly. His greatest legacy to me was the gift of love, knowing not only how to give, but how to receive with love. There have been all kinds of theories about <PERSON>. The boy who never grew up. The man who grew up to his successes. He stimulated and catalyzed all those that brushed his path, but to this day, his motivations remain obscure. People still wonder what made <PERSON> run? I think the answer's simple. He just liked running. He liked to go. I remember that he often used to end his conversations by saying, \"Thanks for listening to me.\"", "words": [{"text": "On", "start": 1.159, "end": 1.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.32, "end": 1.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "March", "start": 1.379, "end": 1.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.659, "end": 1.74, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "22,", "start": 1.74, "end": 2.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.419, "end": 2.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "1958,", "start": 2.659, "end": 3.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.639, "end": 4.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 4.039, "end": 4.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.299, "end": 4.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "flew", "start": 4.299, "end": 4.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.519, "end": 4.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "back", "start": 4.519, "end": 4.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.759, "end": 4.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 4.799, "end": 4.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.859, "end": 4.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "New", "start": 4.88, "end": 5.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.039, "end": 5.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "York", "start": 5.039, "end": 5.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.299, "end": 5.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "from", "start": 5.319, "end": 5.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.46, "end": 5.48, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 5.48, "end": 5.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.599, "end": 5.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "coast", "start": 5.639, "end": 5.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.939, "end": 5.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 5.94, "end": 6.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.019, "end": 6.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 6.019, "end": 6.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.199, "end": 6.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "private", "start": 6.239, "end": 6.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.639, "end": 6.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "plane,", "start": 6.639, "end": 7.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.08, "end": 7.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "The", "start": 7.179, "end": 7.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.299, "end": 7.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Lucky", "start": 7.339, "end": 7.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.619, "end": 7.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 7.679, "end": 8.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.18, "end": 8.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 8.779, "end": 9.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.279, "end": 9.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "had", "start": 9.3, "end": 9.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.4, "end": 9.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 9.42, "end": 9.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.479, "end": 9.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bad", "start": 9.479, "end": 9.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.699, "end": 9.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "case", "start": 9.779, "end": 10.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.0, "end": 10.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 10.019, "end": 10.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.139, "end": 10.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "flu", "start": 10.199, "end": 10.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.44, "end": 10.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 10.46, "end": 10.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.56, "end": 10.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "didn't", "start": 10.579, "end": 10.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.8, "end": 10.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "go", "start": 10.819, "end": 10.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.96, "end": 10.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "with", "start": 10.96, "end": 11.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.099, "end": 11.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "him.", "start": 11.099, "end": 11.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.44, "end": 12.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "The", "start": 12.239, "end": 12.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.34, "end": 12.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "plane", "start": 12.42, "end": 12.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.639, "end": 12.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 12.659, "end": 12.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.84, "end": 12.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "caught", "start": 12.859, "end": 13.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.039, "end": 13.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 13.059, "end": 13.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.139, "end": 13.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 13.159, "end": 13.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.219, "end": 13.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "heavy", "start": 13.219, "end": 13.48, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.48, "end": 13.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "storm", "start": 13.559, "end": 13.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.859, "end": 13.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "over", "start": 13.88, "end": 14.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.079, "end": 14.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "New", "start": 14.079, "end": 14.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.199, "end": 14.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Mexico", "start": 14.259, "end": 14.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.799, "end": 15.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 15.159, "end": 15.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.319, "end": 15.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "crashed.", "start": 15.359, "end": 15.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.939, "end": 16.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "For", "start": 16.479, "end": 16.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.659, "end": 16.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "five", "start": 16.68, "end": 16.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.94, "end": 17.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "days,", "start": 17.02, "end": 17.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.319, "end": 17.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 17.319, "end": 17.48, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.48, "end": 17.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "death", "start": 17.559, "end": 17.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.819, "end": 17.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 17.819, "end": 18.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.02, "end": 18.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "headline", "start": 18.039, "end": 18.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.479, "end": 18.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "news", "start": 18.539, "end": 18.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.84, "end": 18.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "all", "start": 18.84, "end": 18.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.979, "end": 19.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "over", "start": 19.0, "end": 19.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.119, "end": 19.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 19.119, "end": 19.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.219, "end": 19.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "world.", "start": 19.279, "end": 19.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.699, "end": 20.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 20.279, "end": 20.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.519, "end": 20.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 20.579, "end": 20.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.899, "end": 20.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "died", "start": 20.959, "end": 21.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.399, "end": 21.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "as", "start": 21.52, "end": 21.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.679, "end": 21.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 21.68, "end": 21.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.799, "end": 21.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lived,", "start": 21.859, "end": 22.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.299, "end": 22.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spectacularly.", "start": 22.92, "end": 23.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.94, "end": 26.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "His", "start": 26.119, "end": 27.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.5, "end": 28.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "greatest", "start": 28.739, "end": 29.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.079, "end": 29.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "legacy", "start": 29.139, "end": 29.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.579, "end": 29.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 29.639, "end": 29.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.719, "end": 29.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "me", "start": 29.799, "end": 30.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.02, "end": 30.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 30.799, "end": 31.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.0, "end": 31.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 31.019, "end": 31.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.079, "end": 31.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gift", "start": 31.139, "end": 31.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.34, "end": 31.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 31.379, "end": 31.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.479, "end": 31.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "love,", "start": 31.539, "end": 32.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.039, "end": 33.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "knowing", "start": 33.2, "end": 33.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 33.459, "end": 33.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "not", "start": 33.479, "end": 33.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 33.68, "end": 33.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "only", "start": 33.779, "end": 34.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 34.119, "end": 35.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "how", "start": 35.2, "end": 35.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 35.36, "end": 35.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 35.399, "end": 35.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 35.5, "end": 35.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "give,", "start": 35.579, "end": 36.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 36.0, "end": 37.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "but", "start": 37.279, "end": 37.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 37.419, "end": 37.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "how", "start": 37.419, "end": 37.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 37.84, "end": 38.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 38.2, "end": 38.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.339, "end": 38.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "receive", "start": 38.36, "end": 38.88, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.88, "end": 38.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "with", "start": 38.919, "end": 39.06, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.06, "end": 39.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "love.", "start": 39.119, "end": 39.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.539, "end": 57.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "There", "start": 57.54, "end": 59.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.119, "end": 59.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "have", "start": 59.119, "end": 59.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.219, "end": 59.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "been", "start": 59.219, "end": 59.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.38, "end": 59.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "all", "start": 59.399, "end": 59.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.579, "end": 59.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "kinds", "start": 59.719, "end": 60.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.059, "end": 60.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 60.099, "end": 60.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.279, "end": 61.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "theories", "start": 61.059, "end": 61.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.759, "end": 61.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "about", "start": 61.759, "end": 62.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.099, "end": 62.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 62.159, "end": 62.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.679, "end": 64.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "The", "start": 64.4, "end": 64.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.54, "end": 64.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "boy", "start": 64.639, "end": 64.98, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.98, "end": 65.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "who", "start": 65.019, "end": 65.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.199, "end": 65.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "never", "start": 65.279, "end": 65.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.599, "end": 65.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "grew", "start": 65.86, "end": 66.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.08, "end": 66.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up.", "start": 66.139, "end": 66.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.479, "end": 67.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "The", "start": 67.819, "end": 67.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.919, "end": 67.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "man", "start": 67.979, "end": 68.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.319, "end": 68.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "who", "start": 68.339, "end": 68.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.559, "end": 68.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "grew", "start": 68.68, "end": 68.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.859, "end": 68.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up", "start": 68.919, "end": 69.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.139, "end": 69.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 69.159, "end": 69.26, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.26, "end": 69.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 69.26, "end": 69.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.419, "end": 69.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "successes.", "start": 69.459, "end": 70.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.32, "end": 72.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 72.619, "end": 72.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.779, "end": 72.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stimulated", "start": 72.799, "end": 73.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.439, "end": 73.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 73.439, "end": 73.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.599, "end": 73.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "catalyzed", "start": 73.619, "end": 74.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.199, "end": 74.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "all", "start": 74.22, "end": 74.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.36, "end": 74.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "those", "start": 74.36, "end": 74.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.579, "end": 74.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 74.58, "end": 74.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.739, "end": 74.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "brushed", "start": 74.76, "end": 75.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.099, "end": 75.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 75.099, "end": 75.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.239, "end": 75.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "path,", "start": 75.339, "end": 75.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.699, "end": 75.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "but", "start": 75.699, "end": 75.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.879, "end": 75.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 75.919, "end": 76.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.019, "end": 76.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "this", "start": 76.04, "end": 76.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.219, "end": 76.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "day,", "start": 76.299, "end": 76.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.659, "end": 77.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 77.739, "end": 78.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.019, "end": 78.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "motivations", "start": 78.139, "end": 78.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.919, "end": 78.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "remain", "start": 78.919, "end": 79.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.22, "end": 79.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "obscure.", "start": 79.239, "end": 79.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.819, "end": 79.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "People", "start": 79.819, "end": 80.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.119, "end": 80.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "still", "start": 80.119, "end": 80.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.379, "end": 80.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wonder", "start": 80.419, "end": 80.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.719, "end": 81.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "what", "start": 81.979, "end": 82.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.299, "end": 82.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "made", "start": 82.4, "end": 82.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.719, "end": 82.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 82.979, "end": 83.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.22, "end": 83.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 83.299, "end": 83.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.72, "end": 83.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "run?", "start": 83.839, "end": 84.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.399, "end": 86.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 86.299, "end": 86.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.439, "end": 86.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "think", "start": 86.479, "end": 86.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.659, "end": 86.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 86.659, "end": 86.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.779, "end": 86.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "answer's", "start": 86.819, "end": 87.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.219, "end": 87.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "simple.", "start": 87.22, "end": 87.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.699, "end": 89.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 89.699, "end": 89.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 89.879, "end": 89.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "just", "start": 89.919, "end": 90.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 90.059, "end": 90.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "liked", "start": 90.119, "end": 90.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 90.319, "end": 90.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "running.", "start": 90.379, "end": 90.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 90.859, "end": 92.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 92.699, "end": 92.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 92.839, "end": 92.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "liked", "start": 92.879, "end": 93.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.059, "end": 93.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 93.079, "end": 93.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.159, "end": 93.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "go.", "start": 93.239, "end": 93.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.64, "end": 95.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 95.779, "end": 96.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.239, "end": 96.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "remember", "start": 96.239, "end": 96.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.5, "end": 96.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 96.5, "end": 96.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.619, "end": 96.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 96.639, "end": 96.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.739, "end": 96.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "often", "start": 96.779, "end": 96.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.999, "end": 97.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "used", "start": 97.019, "end": 97.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.199, "end": 97.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 97.22, "end": 97.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.379, "end": 98.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "end", "start": 98.259, "end": 98.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.479, "end": 98.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 98.479, "end": 98.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.68, "end": 98.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "conversations", "start": 98.699, "end": 99.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.459, "end": 99.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "by", "start": 99.5, "end": 99.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.699, "end": 99.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "saying,", "start": 99.699, "end": 100.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.079, "end": 100.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "\"Thanks", "start": 100.079, "end": 101.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.319, "end": 101.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 101.36, "end": 101.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.459, "end": 101.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "listening", "start": 101.519, "end": 101.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.839, "end": 101.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 101.879, "end": 101.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.939, "end": 101.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me.\"", "start": 101.979, "end": 102.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 5.10179877281189, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "eng", "language_probability": 0.9968878030776978, "text": "On March 22, 1958, <PERSON> flew back to New York from the coast in his private plane, The Lucky Liz. <PERSON> had a bad case of flu and didn't go with him. The plane was caught in a heavy storm over New Mexico and crashed. For five days, his death was headline news all over the world. <PERSON> died as he lived, spectacularly. His greatest legacy to me was the gift of love, knowing not only how to give, but how to receive with love. There have been all kinds of theories about <PERSON>. The boy who never grew up. The man who grew up to his successes. He stimulated and catalyzed all those that brushed his path, but to this day, his motivations remain obscure. People still wonder what made <PERSON> run? I think the answer's simple. He just liked running. He liked to go. I remember that he often used to end his conversations by saying, \"Thanks for listening to me.\"", "words": [{"text": "On", "start": 1.159, "end": 1.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.32, "end": 1.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "March", "start": 1.379, "end": 1.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.659, "end": 1.74, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "22,", "start": 1.74, "end": 2.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.419, "end": 2.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "1958,", "start": 2.659, "end": 3.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.639, "end": 4.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 4.039, "end": 4.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.299, "end": 4.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "flew", "start": 4.299, "end": 4.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.519, "end": 4.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "back", "start": 4.519, "end": 4.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.759, "end": 4.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 4.799, "end": 4.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.859, "end": 4.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "New", "start": 4.88, "end": 5.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.039, "end": 5.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "York", "start": 5.039, "end": 5.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.299, "end": 5.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "from", "start": 5.319, "end": 5.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.46, "end": 5.48, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 5.48, "end": 5.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.599, "end": 5.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "coast", "start": 5.639, "end": 5.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.939, "end": 5.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 5.94, "end": 6.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.019, "end": 6.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 6.019, "end": 6.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.199, "end": 6.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "private", "start": 6.239, "end": 6.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.639, "end": 6.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "plane,", "start": 6.639, "end": 7.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.08, "end": 7.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "The", "start": 7.179, "end": 7.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.299, "end": 7.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Lucky", "start": 7.339, "end": 7.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.619, "end": 7.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 7.679, "end": 8.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.18, "end": 8.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 8.779, "end": 9.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.279, "end": 9.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "had", "start": 9.3, "end": 9.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.4, "end": 9.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 9.42, "end": 9.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.479, "end": 9.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "bad", "start": 9.479, "end": 9.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.699, "end": 9.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "case", "start": 9.779, "end": 10.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.0, "end": 10.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 10.019, "end": 10.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.139, "end": 10.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "flu", "start": 10.199, "end": 10.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.44, "end": 10.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 10.46, "end": 10.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.56, "end": 10.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "didn't", "start": 10.579, "end": 10.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.8, "end": 10.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "go", "start": 10.819, "end": 10.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.96, "end": 10.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "with", "start": 10.96, "end": 11.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.099, "end": 11.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "him.", "start": 11.099, "end": 11.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.44, "end": 12.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "The", "start": 12.239, "end": 12.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.34, "end": 12.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "plane", "start": 12.42, "end": 12.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.639, "end": 12.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 12.659, "end": 12.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.84, "end": 12.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "caught", "start": 12.859, "end": 13.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.039, "end": 13.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 13.059, "end": 13.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.139, "end": 13.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 13.159, "end": 13.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.219, "end": 13.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "heavy", "start": 13.219, "end": 13.48, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.48, "end": 13.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "storm", "start": 13.559, "end": 13.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.859, "end": 13.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "over", "start": 13.88, "end": 14.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.079, "end": 14.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "New", "start": 14.079, "end": 14.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.199, "end": 14.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Mexico", "start": 14.259, "end": 14.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.799, "end": 15.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 15.159, "end": 15.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.319, "end": 15.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "crashed.", "start": 15.359, "end": 15.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.939, "end": 16.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "For", "start": 16.479, "end": 16.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.659, "end": 16.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "five", "start": 16.68, "end": 16.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.94, "end": 17.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "days,", "start": 17.02, "end": 17.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.319, "end": 17.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 17.319, "end": 17.48, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.48, "end": 17.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "death", "start": 17.559, "end": 17.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.819, "end": 17.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 17.819, "end": 18.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.02, "end": 18.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "headline", "start": 18.039, "end": 18.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.479, "end": 18.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "news", "start": 18.539, "end": 18.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.84, "end": 18.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "all", "start": 18.84, "end": 18.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.979, "end": 19.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "over", "start": 19.0, "end": 19.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.119, "end": 19.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 19.119, "end": 19.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.219, "end": 19.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "world.", "start": 19.279, "end": 19.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.699, "end": 20.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 20.279, "end": 20.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.519, "end": 20.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 20.579, "end": 20.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.899, "end": 20.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "died", "start": 20.959, "end": 21.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.399, "end": 21.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "as", "start": 21.52, "end": 21.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.679, "end": 21.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 21.68, "end": 21.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.799, "end": 21.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lived,", "start": 21.859, "end": 22.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.299, "end": 22.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spectacularly.", "start": 22.92, "end": 23.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.94, "end": 26.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "His", "start": 26.119, "end": 27.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 27.5, "end": 28.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "greatest", "start": 28.739, "end": 29.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.079, "end": 29.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "legacy", "start": 29.139, "end": 29.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.579, "end": 29.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 29.639, "end": 29.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 29.719, "end": 29.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "me", "start": 29.799, "end": 30.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 30.02, "end": 30.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 30.799, "end": 31.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.0, "end": 31.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 31.019, "end": 31.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.079, "end": 31.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gift", "start": 31.139, "end": 31.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.34, "end": 31.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 31.379, "end": 31.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 31.479, "end": 31.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "love,", "start": 31.539, "end": 32.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 32.039, "end": 33.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "knowing", "start": 33.2, "end": 33.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 33.459, "end": 33.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "not", "start": 33.479, "end": 33.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 33.68, "end": 33.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "only", "start": 33.779, "end": 34.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 34.119, "end": 35.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "how", "start": 35.2, "end": 35.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 35.36, "end": 35.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 35.399, "end": 35.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 35.5, "end": 35.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "give,", "start": 35.579, "end": 36.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 36.0, "end": 37.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "but", "start": 37.279, "end": 37.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 37.419, "end": 37.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "how", "start": 37.419, "end": 37.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 37.84, "end": 38.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 38.2, "end": 38.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.339, "end": 38.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "receive", "start": 38.36, "end": 38.88, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 38.88, "end": 38.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "with", "start": 38.919, "end": 39.06, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.06, "end": 39.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "love.", "start": 39.119, "end": 39.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 39.539, "end": 57.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "There", "start": 57.54, "end": 59.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.119, "end": 59.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "have", "start": 59.119, "end": 59.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.219, "end": 59.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "been", "start": 59.219, "end": 59.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.38, "end": 59.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "all", "start": 59.399, "end": 59.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.579, "end": 59.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "kinds", "start": 59.719, "end": 60.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.059, "end": 60.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 60.099, "end": 60.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.279, "end": 61.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "theories", "start": 61.059, "end": 61.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 61.759, "end": 61.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "about", "start": 61.759, "end": 62.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.099, "end": 62.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 62.159, "end": 62.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.679, "end": 64.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "The", "start": 64.4, "end": 64.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.54, "end": 64.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "boy", "start": 64.639, "end": 64.98, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.98, "end": 65.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "who", "start": 65.019, "end": 65.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.199, "end": 65.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "never", "start": 65.279, "end": 65.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.599, "end": 65.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "grew", "start": 65.86, "end": 66.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.08, "end": 66.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up.", "start": 66.139, "end": 66.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.479, "end": 67.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "The", "start": 67.819, "end": 67.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.919, "end": 67.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "man", "start": 67.979, "end": 68.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.319, "end": 68.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "who", "start": 68.339, "end": 68.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.559, "end": 68.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "grew", "start": 68.68, "end": 68.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.859, "end": 68.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up", "start": 68.919, "end": 69.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.139, "end": 69.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 69.159, "end": 69.26, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.26, "end": 69.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 69.26, "end": 69.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.419, "end": 69.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "successes.", "start": 69.459, "end": 70.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.32, "end": 72.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 72.619, "end": 72.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.779, "end": 72.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "stimulated", "start": 72.799, "end": 73.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.439, "end": 73.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 73.439, "end": 73.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 73.599, "end": 73.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "catalyzed", "start": 73.619, "end": 74.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.199, "end": 74.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "all", "start": 74.22, "end": 74.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.36, "end": 74.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "those", "start": 74.36, "end": 74.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.579, "end": 74.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 74.58, "end": 74.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.739, "end": 74.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "brushed", "start": 74.76, "end": 75.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.099, "end": 75.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 75.099, "end": 75.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.239, "end": 75.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "path,", "start": 75.339, "end": 75.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.699, "end": 75.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "but", "start": 75.699, "end": 75.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 75.879, "end": 75.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 75.919, "end": 76.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.019, "end": 76.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "this", "start": 76.04, "end": 76.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.219, "end": 76.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "day,", "start": 76.299, "end": 76.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 76.659, "end": 77.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 77.739, "end": 78.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.019, "end": 78.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "motivations", "start": 78.139, "end": 78.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.919, "end": 78.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "remain", "start": 78.919, "end": 79.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.22, "end": 79.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "obscure.", "start": 79.239, "end": 79.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.819, "end": 79.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "People", "start": 79.819, "end": 80.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.119, "end": 80.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "still", "start": 80.119, "end": 80.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.379, "end": 80.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wonder", "start": 80.419, "end": 80.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.719, "end": 81.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "what", "start": 81.979, "end": 82.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.299, "end": 82.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "made", "start": 82.4, "end": 82.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.719, "end": 82.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 82.979, "end": 83.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.22, "end": 83.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 83.299, "end": 83.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.72, "end": 83.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "run?", "start": 83.839, "end": 84.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.399, "end": 86.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 86.299, "end": 86.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.439, "end": 86.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "think", "start": 86.479, "end": 86.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.659, "end": 86.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 86.659, "end": 86.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 86.779, "end": 86.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "answer's", "start": 86.819, "end": 87.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.219, "end": 87.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "simple.", "start": 87.22, "end": 87.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 87.699, "end": 89.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 89.699, "end": 89.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 89.879, "end": 89.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "just", "start": 89.919, "end": 90.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 90.059, "end": 90.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "liked", "start": 90.119, "end": 90.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 90.319, "end": 90.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "running.", "start": 90.379, "end": 90.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 90.859, "end": 92.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 92.699, "end": 92.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 92.839, "end": 92.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "liked", "start": 92.879, "end": 93.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.059, "end": 93.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 93.079, "end": 93.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.159, "end": 93.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "go.", "start": 93.239, "end": 93.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.64, "end": 95.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 95.779, "end": 96.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.239, "end": 96.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "remember", "start": 96.239, "end": 96.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.5, "end": 96.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 96.5, "end": 96.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.619, "end": 96.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 96.639, "end": 96.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.739, "end": 96.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "often", "start": 96.779, "end": 96.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.999, "end": 97.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "used", "start": 97.019, "end": 97.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.199, "end": 97.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 97.22, "end": 97.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.379, "end": 98.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "end", "start": 98.259, "end": 98.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.479, "end": 98.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 98.479, "end": 98.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.68, "end": 98.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "conversations", "start": 98.699, "end": 99.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.459, "end": 99.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "by", "start": 99.5, "end": 99.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.699, "end": 99.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "saying,", "start": 99.699, "end": 100.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.079, "end": 100.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "\"Thanks", "start": 100.079, "end": 101.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.319, "end": 101.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 101.36, "end": 101.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.459, "end": 101.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "listening", "start": 101.519, "end": 101.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.839, "end": 101.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 101.879, "end": 101.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.939, "end": 101.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me.\"", "start": 101.979, "end": 102.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}]}}, "created_at": 1754281708.959898}