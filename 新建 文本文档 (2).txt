20:18:24] ℹ️ 检测到服务名称: ElevenLabs
[20:18:24] ℹ️ 检测已存在的字幕文件...
[20:18:24] [DEBUG] 🔍 🔍 扫描目录: ./projects\trimmed_Chuvstvitelnyy militsioner AKA The Sentimental Policeman (<PERSON>) (1992)
[20:18:24] [DEBUG] 🔍 🔍 查找模式: trimmed_Chuvstvitelnyy militsioner AKA The Sentimental Policeman (<PERSON>) (1992)_part17_01-20-22-ElevenLabs-*.srt
[20:18:24] [DEBUG] 🔍 🔍 找到 0 个匹配文件
[20:18:24] ℹ️ 未发现已存在字幕文件
[20:18:24] ℹ️ 全部启用的API: ['API2']
[20:18:24] ℹ️ 已存在字幕的API: []
[20:18:24] ℹ️ 需要处理的API: ['API2']
[20:18:24] ℹ️ 正在分析音频静音段...
[20:18:24] ℹ️ 正在检测音频静音段: ./projects\trimmed_Chuvstvitelnyy militsioner AKA The Sentimental Policeman (<PERSON>) (1992)\trimmed_Chuvstvitelnyy militsioner AKA The Sentimental Policeman (<PERSON>) (1992)_part17_01-20-22.mp3
[20:18:25] ℹ️ 检测到 770 个静音段
[20:18:25] ℹ️ 静音检测完成，缓存结果用于后续处理
[20:18:25] ℹ️ 开始处理 API2...
[20:18:25] ℹ️ API2: 启用分块处理
[20:18:25] ℹ️ API2: 分割为 3 个块
[20:18:25] ℹ️ API2: 正在处理第 1/3 块 (991 字符)
[20:18:25] [DEBUG] 🔍 API2: 开始API调用处理第 1 块...
[20:18:25] [DEBUG] 🔍 API2: 开始API调用...
[20:18:57] [DEBUG] 🔍 API2: API调用成功，返回 999 字符
[20:18:57] ℹ️ API2: 第 1 块处理成功 ✅ (耗时: 31.9秒, 差异: 8 字符)
[20:18:57] ℹ️ API2: 正在处理第 2/3 块 (1000 字符)
[20:18:57] ℹ️ API2: 等待请求间隔 10.0 秒...
[20:19:07] [DEBUG] 🔍 API2: 开始API调用处理第 2 块...
[20:19:07] [DEBUG] 🔍 API2: 开始API调用...
[20:19:39] [DEBUG] 🔍 API2: API调用成功，返回 1010 字符
[20:19:39] ℹ️ API2: 第 2 块处理成功 ✅ (耗时: 32.4秒, 差异: 10 字符)
[20:19:39] ℹ️ API2: 正在处理第 3/3 块 (543 字符)
[20:19:39] ℹ️ API2: 等待请求间隔 10.0 秒...
[20:19:49] [DEBUG] 🔍 API2: 开始API调用处理第 3 块...
[20:19:49] [DEBUG] 🔍 API2: 开始API调用...
[20:20:39] [DEBUG] 🔍 API2: API调用成功，返回 548 字符
[20:20:39] ℹ️ API2: 第 3 块处理成功 ✅ (耗时: 49.9秒, 差异: 5 字符)
[20:20:39] ℹ️ API2: 初始处理完成，成功 3/3 块
[20:20:39] ℹ️ API2: LLM调用成功，开始生成字幕...
[20:20:39] ℹ️ 对齐 API2 的文本分段...
[20:20:39] ℹ️ LLM分段调试数据已保存到: ./projects\trimmed_Chuvstvitelnyy militsioner AKA The Sentimental Policeman (Kira Muratova) (1992)\llm_segments_debug_20250804_202039.json
[20:20:39] ℹ️ 开始文本对齐处理（顺序约束），共 89 个片段
[20:20:40] [DEBUG] 🔍 片段处理进度:
[20:20:40] [DEBUG] 🔍   #1 | 精确匹配✅ → 对齐成功✅ | "Ну, хотя и милиционер мой мале..."
[20:20:40] [DEBUG] 🔍   #2 | 精确匹配✅ → 对齐成功✅ | "Почему все стоят?"
[20:20:40] [DEBUG] 🔍   #3 | 精确匹配✅ → 对齐成功✅ | "Нечем дышать?"
[20:20:40] [DEBUG] 🔍   #4 | 精确匹配✅ → 对齐成功✅ | "Ушли?"
[20:20:40] [DEBUG] 🔍   #5 | 精确匹配✅ → 对齐成功✅ | "Ушли, да."
[20:20:40] [DEBUG] 🔍   #6 | 精确匹配✅ → 对齐成功✅ | "А что?"
[20:20:40] [DEBUG] 🔍   #7 | 精确匹配✅ → 对齐成功✅ | "Захарова сегодня появлялась?"
[20:20:40] [DEBUG] 🔍   #8 | 精确匹配✅ → 对齐成功✅ | "Появлялась."
[20:20:40] [DEBUG] 🔍   #9 | 精确匹配✅ → 对齐成功✅ | "Каждый день появляется."
[20:20:40] [DEBUG] 🔍   #10 | 精确匹配✅ → 对齐成功✅ | "Правки у нее уже все есть..."
[20:20:40] [DEBUG] 🔍   #11 | 精确匹配✅ → 对齐成功✅ | "Может быть, нельзя так?"
[20:20:40] [DEBUG] 🔍   #12 | 精确匹配✅ → 对齐成功✅ | "Что значит так нельзя?"
[20:20:40] [DEBUG] 🔍   #13 | 精确匹配✅ → 对齐成功✅ | "Мы же обещали человеку."
[20:20:40] [DEBUG] 🔍   #14 | 精确匹配✅ → 对齐成功✅ | "Как это?"
[20:20:40] [DEBUG] 🔍   #15 | 精确匹配✅ → 对齐成功✅ | "Вы обещали человеку,//никакой ..."
[20:20:40] [DEBUG] 🔍   #16 | 精确匹配✅ → 对齐成功✅ | "Почему он сразу в исполком пош..."
[20:20:40] [DEBUG] 🔍   #17 | 精确匹配✅ → 对齐成功✅ | "Через нашу голову,//сразу же в..."
[20:20:40] [DEBUG] 🔍   #18 | 精确匹配✅ → 对齐成功✅ | "Что тут получилось?"
[20:20:40] [DEBUG] 🔍   #19 | 精确匹配✅ → 对齐成功✅ | "Получили еще и."
[20:20:40] [DEBUG] 🔍   #20 | 精确匹配✅ → 对齐成功✅ | "Мы же лучше знаем,//кому какой..."
[20:20:40] [DEBUG] 🔍   #21 | 精确匹配✅ → 对齐成功✅ | "И что здесь исполком нам не ук..."
[20:20:40] [DEBUG] 🔍   #22 | 精确匹配✅ → 对齐成功✅ | "Это раз."
[20:20:40] [DEBUG] 🔍   #23 | 精确匹配✅ → 对齐成功✅ | "Мы человеку обещали."
[20:20:40] [DEBUG] 🔍   #24 | 精确匹配✅ → 对齐成功✅ | "И это два."
[20:20:40] [DEBUG] 🔍   #25 | 精确匹配✅ → 对齐成功✅ | "Что?"
[20:20:40] [DEBUG] 🔍   #26 | 精确匹配✅ → 对齐成功✅ | "Да, знаю, больше у вас дети не..."
[20:20:40] [DEBUG] 🔍   #27 | 精确匹配✅ → 对齐成功✅ | "У вас всегда, всегда так будет..."
[20:20:40] [DEBUG] 🔍   #28 | 精确匹配✅ → 对齐成功✅ | "Еще кушать а надо гулять,//над..."
[20:20:40] [DEBUG] 🔍   #29 | 精确匹配✅ → 对齐成功✅ | "Девочка моя солнышко,//девушка..."
[20:20:40] [DEBUG] 🔍   #30 | 精确匹配✅ → 对齐成功✅ | "Передайте изоленту."
[20:20:40] [DEBUG] 🔍   #31 | 精确匹配✅ → 对齐成功✅ | "Совет."
[20:20:40] [DEBUG] 🔍   #32 | 精确匹配✅ → 对齐成功✅ | "Вот что?"
[20:20:40] [DEBUG] 🔍   #33 | 精确匹配✅ → 对齐成功✅ | "Изолента на всякий случай."
[20:20:40] [DEBUG] 🔍   #34 | 精确匹配✅ → 对齐成功✅ | "Изолента."
[20:20:40] [DEBUG] 🔍   #35 | 精确匹配✅ → 对齐成功✅ | "Пойдем в другой край,//там пос..."
[20:20:40] [DEBUG] 🔍   #36 | 精确匹配✅ → 对齐成功✅ | "Я этого так не оставлю."
[20:20:40] [DEBUG] 🔍   #37 | 精确匹配✅ → 对齐成功✅ | "Ребенка нашли мы, значит, он н..."
[20:20:40] [DEBUG] 🔍   #38 | 精确匹配✅ → 对齐成功✅ | "Это что за издевательства?"
[20:20:40] [DEBUG] 🔍   #39 | 精确匹配✅ → 对齐成功✅ | "Сначала подбрасывать,//а потом..."
[20:20:41] [DEBUG] 🔍   #40 | 精确匹配✅ → 对齐成功✅ | "Они думают, раз ты милиции слу..."
[20:20:41] [DEBUG] 🔍   #41 | 精确匹配✅ → 对齐成功✅ | "Я этого не позволю!"
[20:20:41] [DEBUG] 🔍   #42 | 精确匹配✅ → 对齐成功✅ | "Я привыкла к этому ребенку."
[20:20:41] [DEBUG] 🔍   #43 | 精确匹配✅ → 对齐成功✅ | "Я не могу без него,//тебе чест..."
[20:20:41] [DEBUG] 🔍   #44 | 精确匹配✅ → 对齐成功✅ | "Я думаю, надо действовать//офи..."
[20:20:41] [DEBUG] 🔍   #45 | 精确匹配✅ → 对齐成功✅ | "Что кстати?"
[20:20:41] [DEBUG] 🔍   #46 | 精确匹配✅ → 对齐成功✅ | "Ребенка нашли мы,//значит, он ..."
[20:20:41] [DEBUG] 🔍   #47 | 精确匹配✅ → 对齐成功✅ | "Это вот ничего?"
[20:20:41] [DEBUG] 🔍   #48 | 精确匹配✅ → 对齐成功✅ | "Ничего."
[20:20:41] [DEBUG] 🔍   #49 | 精确匹配✅ → 对齐成功✅ | "Это что за издевательство?"
[20:20:41] [DEBUG] 🔍   #50 | 精确匹配✅ → 对齐成功✅ | "Сначала подбрасывать,//а потом..."
[20:20:41] [DEBUG] 🔍   #51 | 精确匹配✅ → 对齐成功✅ | "Очень хороший."
[20:20:41] [DEBUG] 🔍   #52 | 精确匹配✅ → 对齐成功✅ | "Потом отбирать."
[20:20:41] [DEBUG] 🔍   #53 | 精确匹配✅ → 对齐成功✅ | "Это что?"
[20:20:41] [DEBUG] 🔍   #54 | 精确匹配✅ → 对齐成功✅ | "Они думают, раз ты милиции слу..."
[20:20:41] [DEBUG] 🔍   #55 | 精确匹配✅ → 对齐成功✅ | "Я этого не позволю!"
[20:20:41] [DEBUG] 🔍   #56 | 精确匹配✅ → 对齐成功✅ | "Я привыкла к этому ребенку."
[20:20:41] [DEBUG] 🔍   #57 | 精确匹配✅ → 对齐成功✅ | "Я не могу без него жить!"
[20:20:41] [DEBUG] 🔍   #58 | 精确匹配✅ → 对齐成功✅ | "Пойдем внутрь, посмотрим."
[20:20:41] [DEBUG] 🔍   #59 | 精确匹配✅ → 对齐成功✅ | "Я думаю, следует действовать//..."
[20:20:41] [DEBUG] 🔍   #60 | 精确匹配✅ → 对齐成功✅ | "Сколько вы, сказали,//стоит ст..."
[20:20:41] [DEBUG] 🔍   #61 | 精确匹配✅ → 对齐成功✅ | "Тридесять."
[20:20:41] [DEBUG] 🔍   #62 | 精确匹配✅ → 对齐成功✅ | "Надо принять ответственные мер..."
[20:20:41] [DEBUG] 🔍   #63 | 精确匹配✅ → 对齐成功✅ | "Да."
[20:20:41] [DEBUG] 🔍   #64 | 精确匹配✅ → 对齐成功✅ | "Да."
[20:20:41] [DEBUG] 🔍   #65 | 精确匹配✅ → 对齐成功✅ | "Она змея подколодная."
[20:20:41] [DEBUG] 🔍   #66 | 精确匹配✅ → 对齐成功✅ | "Главное, главное//никому ни сл..."
[20:20:41] [DEBUG] 🔍   #67 | 精确匹配✅ → 对齐成功✅ | "Ну видишь ли, Клава."
[20:20:41] [DEBUG] 🔍   #68 | 精确匹配✅ → 对齐成功✅ | "Хотя, конечно, змея."
[20:20:41] [DEBUG] 🔍   #69 | 精确匹配✅ → 对齐成功✅ | "Ее как врача пригласили,//она ..."
[20:20:41] [DEBUG] 🔍   #70 | 精确匹配✅ → 对齐成功✅ | "Я думаю, вот этот лучше."
[20:20:41] [DEBUG] 🔍   #71 | 精确匹配✅ → 对齐成功✅ | "Анекдоты все про милиционеров."
[20:20:41] [DEBUG] 🔍   #72 | 精确匹配✅ → 对齐成功✅ | "Не все про милиционеров."
[20:20:41] [DEBUG] 🔍   #73 | 精确匹配✅ → 对齐成功✅ | "Есть и про другие категории ли..."
[20:20:41] [DEBUG] 🔍   #74 | 精确匹配✅ → 对齐成功✅ | "Но надо признать, что милиция/..."
[20:20:41] [DEBUG] 🔍   #75 | 精确匹配✅ → 对齐成功✅ | "Это очень странно, Клава."
[20:20:41] [DEBUG] 🔍   #76 | 精确匹配✅ → 对齐成功✅ | "Это очень странно."
[20:20:41] [DEBUG] 🔍   #77 | 精确匹配✅ → 对齐成功✅ | "Ты иди домой."
[20:20:41] [DEBUG] 🔍   #78 | 精确匹配✅ → 对齐成功✅ | "А куда?"
[20:20:41] [DEBUG] 🔍   #79 | 精确匹配✅ → 对齐成功✅ | "Иди домой."
[20:20:41] [DEBUG] 🔍   #80 | 精确匹配✅ → 对齐成功✅ | "Иди домой."
[20:20:41] [DEBUG] 🔍   #81 | 精确匹配✅ → 对齐成功✅ | "Я сейчас."
[20:20:41] [DEBUG] 🔍   #82 | 精确匹配✅ → 对齐成功✅ | "Более высокой концентрации, че..."
[20:20:41] [DEBUG] 🔍   #83 | 精确匹配✅ → 对齐成功✅ | "Более высокой концентрации, че..."
[20:20:41] [DEBUG] 🔍   #84 | 精确匹配✅ → 对齐成功✅ | "что связано с нарушением функц..."
[20:20:42] [DEBUG] 🔍   #85 | 精确匹配✅ → 对齐成功✅ | "Связано с нарушением функции//..."
[20:20:42] [DEBUG] 🔍   #86 | 精确匹配✅ → 对齐成功✅ | "Связано с нарушением функции//..."
[20:20:42] [DEBUG] 🔍   #87 | 精确匹配✅ → 对齐成功✅ | "Гипоталамической нервной секре..."
[20:20:42] [DEBUG] 🔍   #88 | 精确匹配✅ → 对齐成功✅ | "Нарушение процесса//гипоталами..."
[20:20:42] [DEBUG] 🔍   #89 | 精确匹配✅ → 对齐成功✅ | "Фрагмента."
[20:20:42] ℹ️ API2: 成功对齐 89 个文本分段
[20:20:42] ℹ️ 处理 API2 的字幕条目...
[20:20:42] [DEBUG] 🔍 开始处理 89 个字幕条目的时间间隔分割...
[20:20:42] ℹ️ 时间间隔分割完成：89 个条目 → 90 个条目
[20:20:42] [DEBUG] 🔍 开始两轮条目过滤...
[20:20:42] [DEBUG] 🔍 开始第一轮条目过滤...
[20:20:42] [DEBUG] 🔍 第一轮过滤: 处理90个条目
[20:20:42] [DEBUG] 🔍 第一轮-过滤短时长: 'Что?' (00:00:56,939 --> 00:00:57,079, 持续时间: 0.140s)
[20:20:42] [DEBUG] 🔍 第一轮过滤: 处理90个, 过滤掉1个短时长条目
[20:20:42] ℹ️ 第一轮过滤完成：90 个条目 → 89 个条目（短时长过滤）
[20:20:42] [DEBUG] 🔍 开始第二轮条目过滤...
[20:20:42] [DEBUG] 🔍 第二轮过滤: 处理89个条目
[20:20:42] [DEBUG] 🔍 第二轮-过滤静音重合: 'Нарушение процесса//гипоталамической нервной секреции.' (00:03:41,739 --> 00:03:42,000)
[20:20:42] [DEBUG] 🔍 第二轮过滤: 处理89个, 过滤掉1个静音重合条目
[20:20:42] ℹ️ 第二轮过滤完成：89 个条目 → 88 个条目（静音重合过滤）
[20:20:42] ℹ️ 正在裁剪字幕静音区域...
[20:20:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[20:20:42] [DEBUG] 🔍 第一轮裁剪: 处理88个, 去除静音11.303秒
[20:20:42] [DEBUG] 🔍 开始第二轮静音裁剪...
[20:20:42] [DEBUG] 🔍 第二轮裁剪: 处理88个条目
[20:20:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[20:20:42] [DEBUG] 🔍 第二轮-开始时间: 在100ms偏移找到51个有效调整，剩余37个条目
[20:20:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[20:20:42] [DEBUG] 🔍 第二轮-开始时间: 在200ms偏移找到1个有效调整，剩余36个条目
[20:20:42] [DEBUG] 🔍 第二轮-开始时间: 总计处理52个条目，36个条目保持原样
[20:20:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[20:20:42] [DEBUG] 🔍 第二轮-结束时间: 在100ms偏移找到1个有效调整，剩余87个条目
[20:20:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[20:20:42] [DEBUG] 🔍 第二轮-结束时间: 在200ms偏移找到2个有效调整，剩余85个条目
[20:20:42] [DEBUG] 🔍 第二轮-结束时间: 总计处理3个条目，85个条目保持原样
[20:20:42] [DEBUG] 🔍 第二轮裁剪: 处理88个, 总计调整16个条目
[20:20:42] [DEBUG] 🔍 开始第三轮静音裁剪...
[20:20:42] [DEBUG] 🔍 第三轮裁剪: 处理88个条目
[20:20:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[20:20:42] [DEBUG] 🔍 第三轮-开始时间: 在100ms偏移找到1个有效调整，剩余87个条目
[20:20:42] [DEBUG] 🔍 正在裁剪字幕静音区域...
[20:20:42] [DEBUG] 🔍 第三轮-开始时间: 在200ms偏移找到2个有效调整，剩余85个条目
[20:20:43] [DEBUG] 🔍 第三轮-开始时间: 总计处理3个条目，85个条目保持原样
[20:20:43] [DEBUG] 🔍 正在裁剪字幕静音区域...
[20:20:43] [DEBUG] 🔍 第三轮-结束时间: 在100ms偏移找到52个有效调整，剩余36个条目
[20:20:43] [DEBUG] 🔍 正在裁剪字幕静音区域...
[20:20:43] [DEBUG] 🔍 第三轮-结束时间: 在200ms偏移找到4个有效调整，剩余32个条目
[20:20:43] [DEBUG] 🔍 第三轮-结束时间: 总计处理56个条目，32个条目保持原样
[20:20:43] [DEBUG] 🔍 第三轮裁剪: 处理88个, 总计调整9个条目
[20:20:43] ℹ️ 标准扩充: 增加显示 25.008 秒
[20:20:43] [DEBUG] 🔍 标准扩充详情: 完全28个, 部分60个
[20:20:43] [DEBUG] 🔍 解决扩充冲突: 39个
[20:20:43] ℹ️ 智能扩充: 处理20个短字幕, 增加显示 2.755 秒
[20:20:43] [DEBUG] 🔍 智能扩充详情: 完全13个, 部分7个, 目标1000ms
[20:20:43] ℹ️ 桥接处理: 填补间隔 2.924 秒
[20:20:43] [DEBUG] 🔍 桥接详情: 处理23个间隔, 阈值300ms
[20:20:43] ℹ️ API2: 字幕生成完成 - trimmed_Chuvstvitelnyy militsioner AKA The Sentimental Policeman (Kira Muratova) (1992)_part17_01-20-22-ElevenLabs-API2.srt