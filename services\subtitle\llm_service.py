"""字幕生成LLM服务

基于多LLM API的智能字幕生成服务，将ASR转录结果转换为时间同步的SRT字幕文件。

核心功能：
- LLM智能文本分割
- 时间戳精确对齐
- 字幕条目优化
- SRT格式输出

技术特性：
- 多LLM API故障转移
- 模糊匹配算法
- 时间间隔智能分割
- 增量处理支持
"""

import logging
import os
import json
import requests
import re
import glob
import subprocess
from typing import List, Optional, Dict, Any, Callable, Tuple, Union
from dataclasses import dataclass
from datetime import datetime
from collections import namedtuple

from services.llm.multi_api import MultiLLMService, get_multi_llm_service
from .exceptions import LLMServiceError


logger = logging.getLogger(__name__)

# 数据结构定义
SilenceSegment = namedtuple('SilenceSegment', ['start', 'end'])


@dataclass
class ChunkProcessingResult:
    """分块处理结果数据结构"""
    index: int                          # 块索引
    original_chunk: str                 # 原始文本块
    result: Optional[str] = None        # 处理结果
    success: bool = False               # 是否成功
    retry_count: int = 0                # 重试次数
    character_diff: int = 0             # 字符数差异
    error_message: Optional[str] = None # 错误信息


class SubtitleLLMService:
    """字幕生成LLM服务 - 基于多LLM API的智能字幕生成

    核心功能：
    - 解析ASR转录数据
    - LLM智能文本分割
    - 时间戳精确对齐
    - 字幕条目优化处理
    - SRT格式文件输出

    主要属性：
    - config_mgr: 配置管理器
    - multi_llm_service: 多LLM服务实例
    - max_word_gap: 词汇间时间间隔阈值
    - enable_time_gap_splitting: 时间间隔分割开关
    """

    def __init__(self, multi_llm_service: Optional[MultiLLMService] = None):
        """初始化字幕LLM服务

        Args:
            multi_llm_service: 多LLM服务实例，为None时使用全局单例
        """
        # 配置管理器
        # 简单的配置文件读取
        self._config_data = None

        # 多LLM服务实例
        self.multi_llm_service = multi_llm_service or get_multi_llm_service()

        # 移除回调函数，统一使用Python logging

        # 时间间隔分割配置
        self.max_word_gap: float = 1.5
        self.enable_time_gap_splitting: bool = True

        # 字幕静音裁剪配置
        self.enable_subtitle_silence_trimming: bool = self.get_config('enable_subtitle_silence_trimming', True)
        self.noise_threshold: float = self.get_config('noise_threshold', -30.0)
        self.min_silence_duration: float = self.get_config('min_silence_duration', 0.01)
        self.expand_ms: int = self.get_config('expand_ms', 200)
        self.bridge_ms: int = self.get_config('bridge_ms', 300)
        self.target_duration_ms: int = self.get_config('target_duration_ms', 1000)

        # 静音检测缓存
        self._cached_silences = None
        self._cached_audio_path = None

        # ASR词汇数据缓存
        self.asr_words: List[Dict[str, Any]] = []

        # 日志配置（必须在其他初始化之前）
        self.log_level = self._get_log_level()
        self.log_mode = self._get_log_mode()

        # 匹配统计计数器
        self._exact_match_count = 0
        self._fuzzy_match_count = 0
        self._total_match_attempts = 0

        # 对齐统计计数器
        self._total_alignment_segments = 0
        self._successful_alignment_segments = 0

    def _load_config(self) -> dict:
        """Load configuration file"""
        if self._config_data is None:
            try:
                config_dir = os.path.join(os.path.expanduser("~"), ".evatrans_gui")
                config_file = os.path.join(config_dir, "config.json")
                if os.path.exists(config_file):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        self._config_data = json.load(f)
                else:
                    self._config_data = {}
            except Exception as e:
                print(f"Warning: Failed to load config file: {e}")
                self._config_data = {}
        return self._config_data

    def get_config(self, key: str, default=None):
        """Get configuration value"""
        config = self._load_config()
        return config.get(key, default)

    def update_log_config(self) -> None:
        """更新日志配置"""
        self.log_level = self._get_log_level()
        self.log_mode = self._get_log_mode()

    def _get_log_level(self) -> str:
        """获取日志级别配置"""
        # 方案3：DEBUG始终生成，这里只影响其他级别
        return "INFO"

    def _get_log_mode(self) -> str:
        """获取日志模式配置"""
        # 修复：ConfigManager没有get_config方法，直接返回默认值
        return "SIMPLE"

    def _should_log(self, level: str) -> bool:
        """判断是否输出指定级别日志"""
        levels = {"DEBUG": 0, "INFO": 1, "WARNING": 2, "ERROR": 3}
        current_level = levels.get(self.log_level, 1)
        target_level = levels.get(level, 1)
        return target_level >= current_level

    def _log_debug(self, message: str) -> None:
        """输出调试日志"""
        # 方案3：始终生成DEBUG日志，由UI负责过滤显示
        prefix = "[DEBUG] " if self.log_mode == "DETAILED" else "🔍 "
        logger.debug(f"{prefix}{message}")

    def _log_info(self, message: str) -> None:
        """输出信息日志"""
        if self._should_log("INFO"):
            prefix = "[INFO] " if self.log_mode == "DETAILED" else "ℹ️ "
            logger.info(f"{prefix}{message}")

    def _log_warning(self, message: str) -> None:
        """输出警告日志"""
        if self._should_log("WARNING"):
            prefix = "[WARNING] " if self.log_mode == "DETAILED" else "⚠️ "
            logger.warning(f"{prefix}{message}")

    def _log_error(self, message: str) -> None:
        """输出错误日志"""
        if self._should_log("ERROR"):
            prefix = "[ERROR] " if self.log_mode == "DETAILED" else "❌ "
            logger.error(f"{prefix}{message}")

    def _progress(self, message: str) -> None:
        """更新进度信息

        Args:
            message: 进度消息
        """
        self._log_info(message)

    def _extract_service_name_from_path(self, file_path: str) -> str:
        """从parsed.json文件路径提取ASR服务名称

        Args:
            file_path: parsed.json文件路径

        Returns:
            str: ASR服务名称，无法提取时返回"Unknown"
        """
        file_name = os.path.basename(file_path)
        self._log_debug(f"提取服务名称，输入文件: {file_name}")

        # 正则匹配标准格式
        match = re.search(r'-([\w]+)-parsed\.json$', file_name)
        if match:
            service_name = match.group(1)
            self._log_debug(f"正则匹配成功，服务名称: {service_name}")
            return service_name

        # 备用方案：分割文件名
        parts = file_name.split('-')
        self._log_debug(f"文件名分割结果: {parts}")
        if len(parts) >= 2 and 'parsed.json' in parts[-1]:
            service_name = parts[-2]
            self._log_debug(f"分割匹配成功，服务名称: {service_name}")
            return service_name

        # 无法提取时返回默认值
        self._log_warning(f"无法从文件名 {file_name} 中提取服务名称")
        return "Unknown"

    def _find_existing_subtitles(self, parsed_json_path: str, service_name: str) -> Dict[str, str]:
        """查找已存在的字幕文件

        Args:
            parsed_json_path: parsed.json文件路径
            service_name: ASR服务名称

        Returns:
            Dict[str, str]: API名称到SRT文件路径的映射
        """
        existing_subtitles = {}

        try:
            # 解析路径信息
            base_path = os.path.splitext(parsed_json_path)[0]
            base_dir = os.path.dirname(parsed_json_path)

            # 提取项目基础名称
            parsed_filename = os.path.basename(parsed_json_path)
            parts = parsed_filename.split('-')
            if len(parts) >= 3 and parts[-1] == 'parsed.json':
                base_name = '-'.join(parts[:-2])
            else:
                base_name = os.path.splitext(parsed_filename)[0]
                if base_name.endswith('-parsed'):
                    base_name = base_name[:-7]

            # 使用glob模式匹配直接查找目标文件
            if os.path.exists(base_dir):
                pattern = os.path.join(base_dir, f"{base_name}-{service_name}-*.srt")
                matching_files = glob.glob(pattern)

                self._log_debug(f"🔍 扫描目录: {base_dir}")
                self._log_debug(f"🔍 查找模式: {base_name}-{service_name}-*.srt")
                self._log_debug(f"🔍 找到 {len(matching_files)} 个匹配文件")

                for srt_path in matching_files:
                    file_name = os.path.basename(srt_path)
                    expected_prefix = f"{base_name}-{service_name}-"
                    api_name = file_name[len(expected_prefix):-4]

                    # 验证文件有效性
                    if self._validate_srt_file(srt_path):
                        existing_subtitles[api_name] = srt_path
                        self._log_debug(f"✅ 有效文件: {api_name} -> {file_name}")
                    else:
                        self._log_debug(f"❌ 文件无效: {file_name}")

            # 报告结果
            if existing_subtitles:
                self._log_info(f"发现 {len(existing_subtitles)} 个已存在字幕文件")
            else:
                self._log_info("未发现已存在字幕文件")

        except Exception as e:
            self._log_error(f"检测已存在字幕文件时出错: {e}")
            logger.error(f"Error finding existing subtitles: {e}", exc_info=True)

        return existing_subtitles

    def _validate_srt_file(self, srt_path: str) -> bool:
        """验证SRT文件格式

        Args:
            srt_path: SRT文件路径

        Returns:
            bool: 文件是否为有效SRT格式
        """
        try:
            # 检查文件存在
            if not os.path.exists(srt_path):
                return False

            # 检查文件非空
            if os.path.getsize(srt_path) == 0:
                # 分段文件允许为空
                if '_part' in os.path.basename(srt_path):
                    return True
                return False

            # 读取文件内容
            with open(srt_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            # 检查时间戳格式
            if '-->' not in content:
                return False

            # 检查数字索引
            lines = content.split('\n')
            has_index = False
            for line in lines:
                line = line.strip()
                if line.isdigit():
                    has_index = True
                    break

            return has_index

        except Exception as e:
            logger.error(f"Error validating SRT file {srt_path}: {e}")
            return False

    def _call_llm_api(self,
                     system_prompt: str,
                     user_content: str,
                     task_name: str = "LLM调用",
                     max_tokens: int = 48000) -> Optional[Any]:
        """调用LLM API进行文本处理

        Args:
            system_prompt: 系统提示词
            user_content: 用户输入内容
            task_name: 任务名称
            max_tokens: 最大token数限制

        Returns:
            Optional[Any]: API响应内容，失败时返回None
        """
        try:
            self._log_info(f"开始{task_name}...")


            # 直接使用MultiLLMService，移除故障转移逻辑
            result = self.multi_llm_service.call_api(
                text_to_segment=user_content,
                signals_forwarder=None
            )

            if result:
                self._log_info(f"{task_name}成功")
                return result
            else:
                self._log_error(f"{task_name}失败")
                return None

        except Exception as e:
            self._log_error(f"{task_name}异常: {e}")
            logger.error(f"LLM API call failed: {e}", exc_info=True)
            return None

    # 移除了 _call_single_config 方法，直接使用 MultiLLMService

    def _get_segmentation_prompt(self) -> str:
        """获取LLM文本分割的系统提示词

        Returns:
            str: 系统提示词，定义LLM如何进行文本分割

        功能说明：
        从配置管理器获取预定义的系统提示词，指导LLM将完整的转录文本
        分割为适合字幕显示的片段。提示词包含分割规则、格式要求等。
        """
        return self.get_config('user_llm_system_prompt')

    def _build_llm_payload(self, config: Any, user_prompt: str, system_prompt: str) -> Dict[str, Any]:
        """构造LLM API请求载荷，自动适配不同API格式

        Args:
            config: LLM API配置对象
            user_prompt: 用户提示词（待分割的文本）
            system_prompt: 系统提示词（分割规则）

        Returns:
            Dict[str, Any]: 构造好的API请求载荷

        格式适配：
        - Gemini API：使用system_instruction字段
        - OpenAI API：使用messages数组格式
        - 自动检测API类型并应用相应格式
        """
        max_tokens = getattr(config, "max_tokens", 48000)
        temperature = getattr(config, "temperature", 0.7)
        format_type = str(getattr(config, "format_type", "openai"))

        # 检查format_type是否包含gemini（不区分大小写）
        is_gemini = "gemini" in format_type.lower()

        if is_gemini:
            contents = [
                {"role": "user", "parts": [{"text": user_prompt}]}
            ]
            payload = {
                "contents": contents,
                "generationConfig": {
                    "maxOutputTokens": max_tokens,
                    "temperature": temperature
                }
            }
            if system_prompt:
                # 按Gemini API规范使用system_instruction
                payload["system_instruction"] = {"parts": [{"text": system_prompt}]}
        else:
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": user_prompt})
            payload = {
                "model": getattr(config, "model", ""),
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens
            }
        return payload

    def _build_llm_request(self, config: Any, text_length: int = 0) -> Tuple[str, Dict[str, str], Dict[str, str], int]:
        """构造API请求参数，支持智能超时计算

        Args:
            config: LLM API配置对象
            text_length: 输入文本长度，用于智能超时计算

        Returns:
            Tuple[str, Dict[str, str], Dict[str, str], int]:
                (endpoint_url, headers, params, timeout)
        """
        base_url = getattr(config, "base_url", "").rstrip("/")
        api_key = getattr(config, "api_key", "")
        model = getattr(config, "model", "")
        format_type = str(getattr(config, "format_type", "openai"))

        # 使用固定超时时间
        timeout = 600

        # 检查format_type是否包含gemini（不区分大小写）
        is_gemini = "gemini" in format_type.lower()

        if is_gemini:
            # Gemini API格式
            endpoint = f"{base_url}/v1beta/models/{model}:generateContent"
            headers = {"Content-Type": "application/json"}
            params = {"key": api_key}
        else:
            # OpenAI兼容格式
            endpoint = f"{base_url}/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {api_key.strip()}",
                "Content-Type": "application/json"
            }
            params = {}

        return endpoint, headers, params, timeout

    def _call_api_with_retry(self, endpoint: str, headers: Dict[str, str], params: Dict[str, str],
                           payload: Dict[str, Any], timeout: int, retry_count: int, api_name: str) -> Any:
        """带重试机制的API调用

        Args:
            endpoint: API端点URL
            headers: 请求头
            params: URL参数
            payload: 请求体
            timeout: 超时时间
            retry_count: 重试次数
            api_name: API名称（用于日志）

        Returns:
            API响应的JSON数据

        """
        import time
        import requests

        last_exception = None

        for attempt in range(retry_count + 1):  # +1 因为包含初始尝试
            try:
                if attempt > 0:
                    wait_time = (2 ** (attempt - 1)) * 5  # 5, 10, 20秒
                    self._log_debug(f"{api_name} API第{attempt + 1}次尝试，等待{wait_time}秒...")
                    time.sleep(wait_time)

                self._log_debug(f"{api_name} API调用尝试 {attempt + 1}/{retry_count + 1}")
                response = requests.post(endpoint, headers=headers, params=params, json=payload, timeout=timeout)
                response.raise_for_status()
                return response.json()

            except requests.exceptions.Timeout as e:
                last_exception = e
                self._log_warning(f"{api_name} API超时 (尝试 {attempt + 1}/{retry_count + 1}): {timeout}秒")
                if attempt == retry_count:
                    raise

            except requests.exceptions.HTTPError as e:
                last_exception = e
                status_code = e.response.status_code if e.response else 0

                # 4xx错误不重试（客户端错误，如认证失败）
                if 400 <= status_code < 500:
                    self._log_error(f"{api_name} API客户端错误 {status_code}，不重试")
                    raise

                # 5xx错误重试（服务器错误）
                elif 500 <= status_code < 600:
                    self._log_warning(f"{api_name} API服务器错误 {status_code} (尝试 {attempt + 1}/{retry_count + 1})")
                    if attempt == retry_count:
                        raise
                else:
                    # 其他HTTP错误
                    self._log_error(f"{api_name} API HTTP错误 {status_code}，不重试")
                    raise

            except Exception as e:
                last_exception = e
                self._log_warning(f"{api_name} API异常 (尝试 {attempt + 1}/{retry_count + 1}): {e}")
                if attempt == retry_count:
                    raise

        # 不应该到达这里
        raise last_exception

    def _call_api_with_unified_interval(self, endpoint: str, headers: Dict[str, str],
                                      params: Dict[str, str], payload: Dict[str, Any],
                                      timeout: int, retry_count: int, api_name: str,
                                      request_interval: float) -> Tuple[Any, int]:
        """使用统一间隔的API重试调用

        Args:
            endpoint: API端点URL
            headers: 请求头
            params: URL参数
            payload: 请求体
            timeout: 超时时间
            retry_count: 重试次数
            api_name: API名称
            request_interval: 请求间隔时间（用于重试间隔）

        Returns:
            tuple: (API响应的JSON数据, 实际API调用次数)
        """
        import time
        import requests

        actual_calls = 0

        for attempt in range(retry_count + 1):
            # 每次尝试都计数一次API调用
            actual_calls += 1

            try:
                if attempt > 0:
                    # 重试时使用相同的请求间隔
                    self._log_debug(f"{api_name} 第{attempt + 1}次重试，等待{request_interval}秒...")
                    time.sleep(request_interval)

                response = requests.post(endpoint, headers=headers, params=params,
                                       json=payload, timeout=timeout)
                response.raise_for_status()
                return response.json(), actual_calls

            except requests.exceptions.Timeout as e:
                if attempt == retry_count:
                    raise

            except requests.exceptions.HTTPError as e:
                status_code = e.response.status_code if e.response else 0

                # 4xx错误不重试
                if 400 <= status_code < 500:
                    self._log_error(f"{api_name} 客户端错误 {status_code}，不重试")
                    raise

                # 5xx错误重试
                if attempt == retry_count:
                    raise

            except Exception as e:
                if attempt == retry_count:
                    raise

        raise Exception("不应该到达这里")

    def _should_chunk_text(self, text: str) -> bool:
        """判断是否需要分块处理

        Args:
            text: 输入文本

        Returns:
            bool: True表示需要分块，False表示不需要
        """
        return len(text) > 1000

    def _find_boundary_position(self, text: str, target_pos: int, search_range: int = 100) -> int:
        """在目标位置附近搜索最佳切割边界

        Args:
            text: 文本内容
            target_pos: 目标切割位置
            search_range: 搜索范围

        Returns:
            int: 最佳切割位置（空文本返回0，非空文本保证 > 0 且 <= len(text)）
        """
        # 输入验证和边界检查
        if not text:
            return 0  # 空文本特殊情况，返回0

        text_len = len(text)
        if target_pos <= 0:
            return min(1, text_len)  # 至少前进1个字符
        if target_pos >= text_len:
            return text_len

        # 边界标识符定义
        STRONG_BOUNDARIES = ['.', '!', '?', '。', '！', '？', '；']
        WEAK_BOUNDARIES = [',', '，', '、', ':', '：']
        SPACE_BOUNDARY = [' ']

        # 1. 向前搜索
        start_pos = max(0, target_pos - search_range)

        # 优先级1：强句子标点
        for i in range(target_pos - 1, start_pos - 1, -1):
            if text[i] in STRONG_BOUNDARIES:
                return i + 1  # 在标点之后切割

        # 优先级2：弱标点
        for i in range(target_pos - 1, start_pos - 1, -1):
            if text[i] in WEAK_BOUNDARIES:
                return i + 1  # 在标点之后切割

        # 优先级3：空格
        for i in range(target_pos - 1, start_pos - 1, -1):
            if text[i] in SPACE_BOUNDARY:
                return i + 1  # 在空格之后切割

        # 2. 向后搜索
        end_pos = min(text_len, target_pos + search_range)

        # 优先级1：强句子标点
        for i in range(target_pos, end_pos):
            if text[i] in STRONG_BOUNDARIES:
                return i + 1  # 在标点之后切割

        # 优先级2：弱标点
        for i in range(target_pos, end_pos):
            if text[i] in WEAK_BOUNDARIES:
                return i + 1  # 在标点之后切割

        # 优先级3：空格
        for i in range(target_pos, end_pos):
            if text[i] in SPACE_BOUNDARY:
                return i + 1  # 在空格之后切割

        # 3. 强制切割（确保前进至少1个字符）
        return min(max(target_pos, 1), text_len)

    def _chunk_text(self, text: str, chunk_size: int = 1000) -> List[str]:
        """将文本分块处理

        Args:
            text: 输入文本
            chunk_size: 分块大小阈值

        Returns:
            List[str]: 分块后的文本列表
        """
        if not self._should_chunk_text(text):
            return [text]

        chunks = []
        remaining_text = text

        while len(remaining_text) > chunk_size:
            # 记录当前循环开始时的文本长度
            current_loop_start_length = len(remaining_text)

            # 在chunk_size位置附近找最佳切割点
            cut_pos = self._find_boundary_position(remaining_text, chunk_size)

            # 切割文本（不使用strip，保持原始字符）
            chunk = remaining_text[:cut_pos]
            if chunk:  # 确保块不为空
                chunks.append(chunk)

            # 更新剩余文本（不使用strip，保持原始字符）
            remaining_text = remaining_text[cut_pos:]

            # 立即检测无限循环：检查本次循环是否真的减少了文本长度
            current_loop_end_length = len(remaining_text)
            if current_loop_end_length >= current_loop_start_length:
                self._log_warning(f"分块过程中检测到无限循环，文本长度未减少 ({current_loop_start_length} → {current_loop_end_length})，强制退出")
                break

        # 添加最后一块
        if remaining_text:
            chunks.append(remaining_text)

        return chunks

    def _process_single_chunk(self, chunk_text: str, system_prompt: str,
                            config, api_identifier: str) -> Tuple[Optional[str], int]:
        """处理单个文本块

        Args:
            chunk_text: 要处理的文本块
            system_prompt: 系统提示词
            config: API配置
            api_identifier: API标识符

        Returns:
            tuple: (处理结果, API调用次数)，失败时返回(None, 调用次数)
        """
        try:
            # 构建请求
            payload = self._build_llm_payload(config, chunk_text, system_prompt)
            endpoint, headers, params, timeout = self._build_llm_request(config, len(chunk_text))

            # 获取配置参数
            retry_count = getattr(config, 'retry_count', 3)
            request_interval = getattr(config, 'request_interval', 10.0)

            self._log_debug(f"{api_identifier}: 开始API调用...")

            # 使用统一间隔的重试机制
            data, api_calls = self._call_api_with_unified_interval(
                endpoint=endpoint,
                headers=headers,
                params=params,
                payload=payload,
                timeout=timeout,
                retry_count=retry_count,
                api_name=api_identifier,
                request_interval=request_interval
            )

            # 解析响应
            format_type = str(getattr(config, 'format_type', 'openai'))
            content = self._parse_content_from_response(data, format_type)

            if content:
                self._log_debug(f"{api_identifier}: API调用成功，返回 {len(content)} 字符")
            else:
                self._log_warning(f"{api_identifier}: API调用返回空内容")

            return content, api_calls

        except Exception as e:
            self._log_error(f"{api_identifier} 处理文本块失败: {e}")
            # 异常情况下，尝试从异常中获取调用次数，否则返回1
            return None, 1

    def _merge_chunk_results(self, chunk_results: List[str]) -> str:
        """合并分块处理结果

        Args:
            chunk_results: 各个块的处理结果列表

        Returns:
            str: 合并后的结果
        """
        merged_segments = []

        for chunk_result in chunk_results:
            # 每个chunk_result是LLM返回的分割文本
            # 按行分割并过滤空行
            segments = [seg.strip() for seg in chunk_result.split('\n') if seg.strip()]
            merged_segments.extend(segments)

        # 用换行符连接所有段落
        return '\n'.join(merged_segments)

    def _validate_chunk_result(self, original_chunk: str, result_chunk: str) -> bool:
        """验证块处理结果的有效性

        Args:
            original_chunk: 原始文本块
            result_chunk: 处理结果

        Returns:
            bool: True表示结果有效，False表示需要重试
        """
        if not result_chunk or not result_chunk.strip():
            return False  # 空结果直接失败

        original_length = len(original_chunk)
        result_length = len(result_chunk)
        diff = abs(original_length - result_length)

        return diff <= 120  # 字符数差异在120以内认为合格

    def _process_chunks_with_validation(self, full_text: str, system_prompt: str,
                                      config, api_identifier: str) -> Dict[str, Any]:
        """带有效性验证的分块处理

        Args:
            full_text: 完整文本
            system_prompt: 系统提示词
            config: API配置
            api_identifier: API标识符

        Returns:
            Dict: 包含处理结果、成功率、统计信息
        """
        import time

        # 记录开始时间
        start_time = time.time()
        api_calls = 0

        # 1. 分块
        chunks = self._chunk_text(full_text)
        self._log_info(f"{api_identifier}: 分割为 {len(chunks)} 个块")

        # 2. 初始化结果跟踪
        chunk_results = []
        request_interval = getattr(config, 'request_interval', 10.0)

        for i, chunk in enumerate(chunks):
            chunk_result = ChunkProcessingResult(
                index=i,
                original_chunk=chunk
            )
            chunk_results.append(chunk_result)

        # 3. 初始处理每个块
        for i, chunk_result in enumerate(chunk_results):
            self._log_info(f"{api_identifier}: 正在处理第 {i+1}/{len(chunks)} 块 ({len(chunk_result.original_chunk)} 字符)")

            # 应用请求间隔（除了第一个块）
            if i > 0:
                self._log_info(f"{api_identifier}: 等待请求间隔 {request_interval} 秒...")
                time.sleep(request_interval)

            # 处理单个块
            self._log_debug(f"{api_identifier}: 开始API调用处理第 {i+1} 块...")
            chunk_start_time = time.time()

            try:
                result, chunk_api_calls = self._process_single_chunk(
                    chunk_result.original_chunk, system_prompt, config, api_identifier
                )
                # 累加实际的API调用次数
                api_calls += chunk_api_calls

                chunk_time = time.time() - chunk_start_time

                if result and self._validate_chunk_result(chunk_result.original_chunk, result):
                    chunk_result.result = result
                    chunk_result.success = True
                    chunk_result.character_diff = abs(len(chunk_result.original_chunk) - len(result))
                    self._log_info(f"{api_identifier}: 第 {i+1} 块处理成功 ✅ (耗时: {chunk_time:.1f}秒, 差异: {chunk_result.character_diff} 字符)")
                else:
                    chunk_result.success = False
                    chunk_result.character_diff = len(chunk_result.original_chunk) if not result else abs(len(chunk_result.original_chunk) - len(result))
                    chunk_result.error_message = "处理结果无效" if result else "处理返回空结果"
                    self._log_warning(f"{api_identifier}: 第 {i+1} 块处理失败 ❌ (耗时: {chunk_time:.1f}秒, {chunk_result.error_message})")

            except Exception as e:
                chunk_time = time.time() - chunk_start_time
                chunk_result.success = False
                chunk_result.error_message = str(e)
                chunk_result.character_diff = len(chunk_result.original_chunk)
                # 异常情况下不额外计数，因为_process_single_chunk内部已经处理了计数
                self._log_error(f"{api_identifier}: 第 {i+1} 块处理异常 ❌ (耗时: {chunk_time:.1f}秒) {e}")

        # 4. 初始处理完成，统计结果
        success_count = sum(1 for cr in chunk_results if cr.success)
        self._log_info(f"{api_identifier}: 初始处理完成，成功 {success_count}/{len(chunks)} 块")

        # 重试失败的块（最多3次）
        failed_chunks = [cr for cr in chunk_results if not cr.success]

        if failed_chunks:
            self._log_info(f"{api_identifier}: 开始重试 {len(failed_chunks)} 个失败的块")

        # 全局API调用时间控制，确保所有重试都遵循间隔
        global_last_api_call_time = time.time()

        for chunk_result in failed_chunks:

            for retry_attempt in range(3):  # 最多重试3次
                self._log_warning(f"{api_identifier}: 重试第 {chunk_result.index+1} 块 (第{retry_attempt+1}次重试)")

                # 全局智能间隔控制：确保所有API调用之间有足够间隔
                current_time = time.time()
                time_since_last_call = current_time - global_last_api_call_time

                if time_since_last_call < request_interval:
                    sleep_time = request_interval - time_since_last_call
                    self._log_info(f"{api_identifier}: 等待重试间隔 {sleep_time:.1f} 秒...")
                    time.sleep(sleep_time)

                try:
                    retry_result, retry_api_calls = self._process_single_chunk(
                        chunk_result.original_chunk, system_prompt, config,
                        f"{api_identifier}-重试{retry_attempt+1}"
                    )

                    # 累加实际的API调用次数
                    api_calls += retry_api_calls

                    # 更新全局最后API调用时间（无论成功失败都更新）
                    global_last_api_call_time = time.time()

                    if retry_result and self._validate_chunk_result(chunk_result.original_chunk, retry_result):
                        chunk_result.result = retry_result
                        chunk_result.success = True
                        chunk_result.character_diff = abs(len(chunk_result.original_chunk) - len(retry_result))
                        chunk_result.retry_count = retry_attempt + 1  # 记录成功时的重试次数
                        chunk_result.error_message = None
                        self._log_info(f"{api_identifier}: 第 {chunk_result.index+1} 块重试成功")
                        break  # 成功后跳出重试循环
                    else:
                        # 重试失败时，保留初始错误消息并追加重试信息
                        retry_error = "重试结果仍无效" if retry_result else "重试返回空结果"
                        if chunk_result.error_message and "重试" not in chunk_result.error_message:
                            chunk_result.error_message = f"{chunk_result.error_message}; {retry_error}"
                        else:
                            chunk_result.error_message = retry_error

                        # 如果是最后一次重试，记录重试次数
                        if retry_attempt == 2:  # 最后一次重试（0,1,2）
                            chunk_result.retry_count = 3

                except Exception as e:
                    # 异常情况下也要计数API调用（通过_process_single_chunk已经计数）
                    # 这里不需要额外计数，因为_process_single_chunk内部已经处理了

                    # 更新全局最后API调用时间（异常情况下也要更新）
                    global_last_api_call_time = time.time()

                    # 保留初始错误消息并追加重试异常
                    retry_error = f"重试异常: {e}"
                    if chunk_result.error_message and "重试" not in chunk_result.error_message:
                        chunk_result.error_message = f"{chunk_result.error_message}; {retry_error}"
                    else:
                        chunk_result.error_message = retry_error

                    self._log_error(f"{api_identifier}: 第 {chunk_result.index+1} 块重试异常: {e}")

                    # 如果是最后一次重试，记录重试次数
                    if retry_attempt == 2:  # 最后一次重试（0,1,2）
                        chunk_result.retry_count = 3

        # 5. 生成处理统计
        end_time = time.time()
        processing_stats = {
            'total_time': end_time - start_time,
            'api_calls': api_calls
        }

        # 6. 合并有效结果
        valid_results = [cr.result for cr in chunk_results if cr.success and cr.result]
        final_result = '\n'.join(valid_results) if valid_results else ""

        # 7. 计算成功率
        success_count = sum(1 for cr in chunk_results if cr.success)
        success_rate = success_count / len(chunk_results) if chunk_results else 0

        return {
            "success": success_count > 0,  # 至少有一个块成功
            "result": final_result,
            "success_rate": success_rate,
            "chunk_results": chunk_results,
            "processing_stats": processing_stats
        }

    def _parse_content_from_response(self, response_data: Dict[str, Any], format_type: str) -> Optional[str]:
        """从API响应中提取文本内容"""
        try:
            # 检查format_type是否包含gemini（不区分大小写）
            is_gemini = "gemini" in format_type.lower()

            if is_gemini:
                if "candidates" in response_data and response_data["candidates"]:
                    return response_data["candidates"][0].get("content", {}).get("parts", [{}])[0].get("text", "")
            else:  # openai
                if "choices" in response_data and response_data["choices"]:
                    return response_data["choices"][0].get("message", {}).get("content", "")
            return None
        except Exception as e:
            self._log_error(f"解析API响应异常: {e}")
            return None

    def _align_segments_with_timestamps(self, segments: List[str], asr_words: List[Dict[str, Any]], project_dir: str = None) -> Tuple[List[Tuple[float, float, str]], List[str]]:
        """将LLM分割的文本片段与ASR时间戳对齐

        使用精确匹配和动态锚点匹配的两轮策略，将LLM处理后的文本片段
        与ASR识别的词汇进行对齐，为每个文本片段分配准确的开始和结束时间戳。

        Args:
            segments: LLM分割的文本片段列表
            asr_words: ASR词汇列表，每个词汇包含text、start、end字段
            project_dir: 项目目录路径，用于保存调试文件

        Returns:
            Tuple[List[Tuple[float, float, str]], List[str]]:
                - 字幕条目列表，每个条目包含(开始时间, 结束时间, 文本内容)
                - 对齐过程的日志信息列表
        """
        try:
            # 缓存ASR词汇数据供后续时间间隔分割使用
            self.asr_words = asr_words

            # 保存LLM分段调试数据
            self._save_llm_segments_debug(segments, project_dir)

            results = []
            log_lines = []
            last_end_word_index = -1  # 上一个匹配结束的词汇索引（顺序约束）

            # 收集片段处理结果用于结构化日志输出
            segment_results = []

            self._log_info(f"开始文本对齐处理（顺序约束），共 {len(segments)} 个片段")

            for i, segment in enumerate(segments):
                if not segment.strip():
                    continue

                # 应用顺序约束：只在上一个位置之后搜索
                search_start_index = last_end_word_index + 1

                if search_start_index >= len(asr_words):
                    log_lines.append(f"片段{i+1}: 搜索范围超出语料库")

                    # 收集结果
                    segment_results.append({
                        'index': i + 1,
                        'match_type': "搜索范围超出",
                        'align_result': "跳过处理",
                        'text_preview': segment[:30] + "..." if len(segment) > 30 else segment
                    })
                    continue

                # 执行带顺序约束的文本匹配
                matched_words, next_index, match_type = self._get_segment_words_with_constraints(
                    segment.strip(), asr_words, search_start_index
                )

                if matched_words:
                    # 更新顺序约束位置
                    last_end_word_index = asr_words.index(matched_words[-1])

                    # 提取时间戳并创建字幕条目
                    start_time = matched_words[0]['start_time']
                    end_time = matched_words[-1]['end_time']
                    results.append((start_time, end_time, segment.strip()))

                    # 生成对齐日志信息
                    asr_text = ' '.join(w['word'] for w in matched_words)
                    start_word_index = asr_words.index(matched_words[0])
                    log_lines.append(f"片段{i+1}: "
                                   f"时间={start_time:.3f}-{end_time:.3f}s "
                                   f"词汇位置={start_word_index}-{last_end_word_index} "
                                   f"ASR文本=[{asr_text[:30]}...]")

                    # 收集成功结果
                    segment_results.append({
                        'index': i + 1,
                        'match_type': match_type,
                        'align_result': "对齐成功",
                        'text_preview': segment[:30] + "..." if len(segment) > 30 else segment
                    })
                else:
                    log_lines.append(f"片段{i+1}: 匹配失败 - {segment[:30]}...")

                    # 收集失败结果
                    segment_results.append({
                        'index': i + 1,
                        'match_type': match_type,
                        'align_result': "对齐失败",
                        'text_preview': segment[:30] + "..." if len(segment) > 30 else segment
                    })

                    # 失败的分段直接放弃，不推进搜索位置
                    # last_end_word_index 保持不变，让后续分段有机会匹配同样的语料库内容

            # 输出结构化片段处理进度日志
            if segment_results:
                self._log_debug("片段处理进度:")
                for result in segment_results:
                    # 使用emoji符号让日志更直观
                    match_emoji = "✅" if "成功" in result['align_result'] else "❌"
                    match_symbol = "✅" if result['match_type'] in ["精确匹配", "锚点匹配"] else "❌"

                    self._log_debug(f"  #{result['index']} | {result['match_type']}{match_symbol} → {result['align_result']}{match_emoji} | \"{result['text_preview']}\"")

            # 统计对齐结果
            success_count = len(results)
            success_rate = success_count / len(segments) * 100 if segments else 0

            # 累积对齐统计数据
            self._total_alignment_segments += len(segments)
            self._successful_alignment_segments += success_count

            return results, log_lines

        except Exception as e:
            self._log_error(f"文本对齐失败: {str(e)}")
            raise LLMServiceError(f"文本对齐失败: {str(e)}")

    def _save_llm_segments_debug(self, segments: List[str], project_dir: str = None) -> None:
        """保存LLM分段调试数据

        将LLM分割的文本片段保存到调试文件中，用于后续分析对齐问题。

        Args:
            segments: LLM分割的文本片段列表
            project_dir: 项目目录路径，用于确定保存位置
        """
        try:
            import json
            from datetime import datetime

            # 构建调试数据
            debug_data = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "total_segments": len(segments),
                "project_dir": project_dir,
                "segments": []
            }

            # 处理每个片段
            for index, segment in enumerate(segments, 1):
                cleaned_text = self._clean_text_for_matching(segment)
                segment_info = {
                    "index": index,
                    "original_text": segment,
                    "cleaned_text": cleaned_text,
                    "original_length": len(segment),
                    "cleaned_length": len(cleaned_text)
                }
                debug_data["segments"].append(segment_info)

            # 确定保存路径
            if project_dir and os.path.exists(project_dir):
                save_dir = project_dir
            else:
                # 如果没有项目路径，使用当前工作目录
                save_dir = os.getcwd()

            # 构建文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"llm_segments_debug_{timestamp}.json"
            debug_file_path = os.path.join(save_dir, filename)

            # 保存文件
            with open(debug_file_path, 'w', encoding='utf-8') as f:
                json.dump(debug_data, f, ensure_ascii=False, indent=2)

            self._log_info(f"LLM分段调试数据已保存到: {debug_file_path}")

        except Exception as e:
            # 调试功能失败不应影响主流程
            self._log_warning(f"保存LLM分段调试数据失败: {str(e)}")

    def _clean_text_for_matching(self, text: str) -> str:
        """清理文本用于匹配算法

        移除空格和//分隔符以提高文本匹配的准确性。

        Args:
            text: 待清理的原始文本

        Returns:
            str: 清理后的文本，移除空格和//分隔符
        """
        # 移除所有空格
        cleaned = text.replace(" ", "")

        # 移除//分隔符
        cleaned = cleaned.replace("//", "")

        return cleaned

    def _anchor_based_match(self, corpus: str, segment_clean: str) -> int:
        """基于3字符锚点的匹配算法（第一个合格策略）"""

        # 处理极短文本
        if len(segment_clean) < 2:
            return corpus.find(segment_clean)

        QUALIFIED_THRESHOLD = 0.7
        start_anchor = segment_clean[:3]
        end_anchor = segment_clean[-3:]
        segment_len = len(segment_clean)

        # 按位置从前往后搜索，找到第一个合格的就返回
        for start_pos in range(len(corpus) - len(start_anchor) + 1):
            # 检查开头锚点
            if corpus[start_pos:start_pos+len(start_anchor)] != start_anchor:
                continue  # 这个位置开头不匹配，跳过

            # 在这个开头位置寻找合适的结尾
            if self._find_qualified_end(corpus, start_pos, end_anchor, segment_clean, QUALIFIED_THRESHOLD):
                return start_pos  # 找到第一个合格的，立即返回

        return -1  # 没有找到合格的匹配

    def _find_qualified_end(self, corpus: str, start_pos: int, end_anchor: str,
                           segment_clean: str, threshold: float) -> bool:
        """在指定开头位置寻找合格的结尾"""

        segment_len = len(segment_clean)
        min_end_pos = start_pos + max(1, segment_len - 3)
        max_end_pos = start_pos + segment_len + 3
        max_end_pos = min(max_end_pos, len(corpus) - 1)

        # 按位置从前往后寻找结尾锚点
        for end_pos in range(min_end_pos, max_end_pos + 1):
            # 检查结尾锚点
            if end_pos < len(end_anchor) - 1:
                continue
            if corpus[end_pos-len(end_anchor)+1:end_pos+1] != end_anchor:
                continue

            # 找到了结尾锚点，评估整体匹配
            candidate = corpus[start_pos:end_pos + 1]
            score = self._calculate_match_score(candidate, segment_clean)

            if score >= threshold:
                return True  # 找到合格的结尾

        return False  # 没有找到合格的结尾

    def _get_segment_words_with_constraints(self, text_segment: str, all_asr_words: List[Dict[str, Any]], start_word_index: int) -> Tuple[List[Dict[str, Any]], int, str]:
        """在约束范围内执行两轮文本匹配

        使用精确匹配和动态锚点匹配的两轮策略，在指定的搜索范围内
        找到与LLM文本片段最匹配的ASR词汇序列。

        Args:
            text_segment: 待匹配的LLM文本片段
            all_asr_words: 完整的ASR词汇列表
            start_word_index: 搜索起始位置索引

        Returns:
            Tuple[List[Dict[str, Any]], int, str]: 匹配的ASR词汇列表、下一个搜索位置和匹配类型
        """

        # 构建受限的搜索范围
        search_words = all_asr_words[start_word_index:]
        if not search_words:
            return [], start_word_index, "搜索范围为空"

        # 构建受限语料库
        corpus = "".join([w['word'] for w in search_words])
        segment_clean = self._clean_text_for_matching(text_segment)

        if not segment_clean:
            return [], start_word_index, "文本清理后为空"

        # 构建字符到词汇的映射（用于边界计算）
        char_to_word_map = {}
        char_index = 0
        for word_idx, word_obj in enumerate(search_words):
            word_text = word_obj['word']
            for char_offset in range(len(word_text)):
                char_to_word_map[char_index + char_offset] = word_obj
            char_index += len(word_text)

        # 执行两轮匹配：精确匹配 + 动态锚点匹配
        self._total_match_attempts += 1
        match_type = "匹配失败"

        import time
        start_time = time.perf_counter()

        # 第一轮：精确匹配
        exact_start = time.perf_counter()
        match_start_char_index = corpus.find(segment_clean)
        exact_time = time.perf_counter() - exact_start

        if match_start_char_index != -1:
            self._exact_match_count += 1
            match_type = "精确匹配"
        else:
            # 第二轮：锚点匹配
            anchor_start = time.perf_counter()
            match_start_char_index = self._anchor_based_match(corpus, segment_clean)
            anchor_time = time.perf_counter() - anchor_start

            if match_start_char_index != -1:
                self._fuzzy_match_count += 1
                match_type = "锚点匹配"
            else:
                self._log_warning(f"两轮匹配都失败。LLM片段: \"{text_segment}\"")
                return [], start_word_index, match_type

        # 计算精确的匹配范围和相似度
        matched_corpus_text = corpus[match_start_char_index:match_start_char_index + len(segment_clean)]

        # 从字符索引映射回词汇对象
        first_word_obj = char_to_word_map.get(match_start_char_index)
        last_char_index = match_start_char_index + len(segment_clean) - 1
        last_word_obj = char_to_word_map.get(last_char_index)

        if not first_word_obj or not last_word_obj:
            self._log_error(f"无法从字符位置映射回词汇对象")
            return [], start_word_index, match_type

        # 查找词汇在原始列表中的索引位置（需要加上偏移）
        try:
            first_word_global_idx = all_asr_words.index(first_word_obj)
            last_word_global_idx = all_asr_words.index(last_word_obj)
        except ValueError:
            self._log_error(f"在ASR词汇列表中找不到边界词汇对象")
            return [], start_word_index, match_type

        # 提取匹配的词汇序列
        best_match_words = all_asr_words[first_word_global_idx:last_word_global_idx + 1]
        best_match_end_index = last_word_global_idx + 1

        return best_match_words, best_match_end_index, match_type

    def _is_reasonable_length(self, actual_length: int, expected_length: int) -> bool:
        """检查长度是否合理"""
        min_length = max(1, expected_length - 3)  # 最少不超过expected-3
        max_length = expected_length + 3          # 最多不超过expected+3
        return min_length <= actual_length <= max_length

    def _calculate_match_score(self, candidate: str, segment: str) -> float:
        """计算匹配分数"""
        # 字符集合相似度（Jaccard）
        candidate_chars = set(candidate)
        segment_chars = set(segment)

        intersection = len(candidate_chars & segment_chars)
        union = len(candidate_chars | segment_chars)

        if union == 0:
            return 1.0 if len(candidate) == 0 and len(segment) == 0 else 0.0

        jaccard_score = intersection / union

        # 长度相似度
        length_diff = abs(len(candidate) - len(segment))
        max_len = max(len(candidate), len(segment))
        length_score = 1 - (length_diff / max_len) if max_len > 0 else 1.0

        # 综合分数（Jaccard 80% + 长度 20%）
        final_score = jaccard_score * 0.8 + length_score * 0.2

        return final_score

    def _log_matching_statistics(self):
        """记录匹配和对齐统计信息"""
        if self._total_match_attempts > 0:
            exact_rate = (self._exact_match_count / self._total_match_attempts) * 100
            anchor_rate = (self._fuzzy_match_count / self._total_match_attempts) * 100

            # 计算对齐统计
            alignment_rate = (self._successful_alignment_segments / self._total_alignment_segments * 100) if self._total_alignment_segments > 0 else 0

            # 计算匹配成功总数和成功率
            match_success_count = self._exact_match_count + self._fuzzy_match_count
            match_success_rate = (match_success_count / self._total_match_attempts) * 100

            self._log_info(f"文本处理统计:")
            self._log_info(f"  总LLM分段数: {self._total_match_attempts}")
            self._log_info(f"  第一轮精确匹配: {self._exact_match_count}/{self._total_match_attempts} ({exact_rate:.1f}%)")
            self._log_info(f"  第二轮锚点匹配: {self._fuzzy_match_count}/{self._total_match_attempts} ({anchor_rate:.1f}%)")
            self._log_info(f"  文本匹配成功: {match_success_count}/{self._total_match_attempts} ({match_success_rate:.1f}%)")
            self._log_info(f"  文本对齐成功: {self._successful_alignment_segments}/{self._total_alignment_segments} ({alignment_rate:.1f}%)")

    def _find_audio_file(self, base_path: str) -> str:
        """查找实际的音频文件名

        在项目目录中查找音频文件，支持常见的音频格式。

        Args:
            base_path: 项目基础路径（不含扩展名）

        Returns:
            str: 实际的音频文件名，如果找不到则返回默认名称
        """
        # 常见音频格式扩展名（MP3优先，因为一体化处理器统一输出MP3）
        audio_extensions = ['.mp3', '.aac', '.wav', '.m4a', '.flac', '.ogg']

        project_name = os.path.basename(base_path)
        project_dir = os.path.dirname(base_path)

        # 在项目目录中查找音频文件
        for ext in audio_extensions:
            audio_file_path = os.path.join(project_dir, project_name + ext)
            if os.path.exists(audio_file_path):
                return project_name + ext

        # 如果找不到，返回默认的wav文件名
        return project_name + ".wav"

    def generate_subtitle(self, parsed_json_path: str, subtitle_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """从ASR解析数据生成SRT字幕文件

        主要处理流程：
        1. 加载和验证ASR解析数据
        2. 调用LLM进行文本智能分割
        3. 对齐文本片段与时间戳
        4. 优化字幕条目时间间隔
        5. 生成标准SRT格式文件

        Args:
            parsed_json_path: ASR解析数据文件路径
            subtitle_config: 字幕生成配置参数

        Returns:
            Dict[str, Any]: 包含成功状态和结果信息的字典
        """
        try:
            self._log_info(f"开始字幕生成: {parsed_json_path}")

            # 重置统计计数器
            self._exact_match_count = 0
            self._fuzzy_match_count = 0
            self._total_match_attempts = 0
            self._total_alignment_segments = 0
            self._successful_alignment_segments = 0

            # 提取服务名称
            service_name = self._extract_service_name_from_path(parsed_json_path)
            self._log_info(f"检测到服务名称: {service_name}")

            # 读取JSON文件
            try:
                if not os.path.exists(parsed_json_path):
                    raise FileNotFoundError(f"文件不存在: {parsed_json_path}")

                with open(parsed_json_path, 'r', encoding='utf-8') as f:
                    parsed_data = json.load(f)
            except FileNotFoundError as e:
                error_msg = f"{e}"
                self._log_error(error_msg)
                return {"success": False, "error": error_msg}
            except json.JSONDecodeError as e:
                error_msg = f"JSON解析错误: {e}"
                self._log_error(error_msg)
                return {"success": False, "error": error_msg}
            except Exception as e:
                error_msg = f"读取文件错误: {e}"
                self._log_error(error_msg)
                logger.error(f"Error reading JSON file: {e}", exc_info=True)
                return {"success": False, "error": error_msg}

            # 验证JSON结构
            if not isinstance(parsed_data, dict):
                error_msg = "无效JSON格式"
                self._log_error(error_msg)
                return {"success": False, "error": error_msg}

            # 提取数据
            full_text = parsed_data.get('full_text', '')
            words = parsed_data.get('words', [])

            if not full_text or not words:
                error_msg = "JSON文件缺少必要字段"
                self._log_error(error_msg)
                return {"success": False, "error": error_msg}

            # 构建路径
            parsed_filename = os.path.basename(parsed_json_path)
            base_dir = os.path.dirname(parsed_json_path)

            # 解析文件名
            parts = parsed_filename.split('-')
            if len(parts) >= 3 and parts[-1] == 'parsed.json':
                base_name = '-'.join(parts[:-2])
            else:
                base_name = os.path.splitext(parsed_filename)[0]
                if base_name.endswith('-parsed'):
                    base_name = base_name[:-7]

            base_path = os.path.join(base_dir, base_name)
            task_folder = os.path.dirname(parsed_json_path)

            # 检测已存在的字幕文件
            self._progress("检测已存在的字幕文件...")
            existing_subtitles = self._find_existing_subtitles(parsed_json_path, service_name)

            # 获取所有启用的API配置
            active_configs = self.multi_llm_service.llm_service.get_active_configs()
            if not active_configs:
                error_msg = "没有可用的LLM API配置，请在设置中配置至少一个LLM API"
                self._log_error(error_msg)
                return {"success": False, "error": error_msg}

            # 计算需要处理的API
            all_api_names = [config.name for config in active_configs]
            existing_api_names = list(existing_subtitles.keys())
            missing_api_names = [name for name in all_api_names if name not in existing_api_names]

            self._log_info(f"全部启用的API: {all_api_names}")
            self._log_info(f"已存在字幕的API: {existing_api_names}")
            self._log_info(f"需要处理的API: {missing_api_names}")

            # 字幕静音裁剪：预先进行静音检测
            if self.enable_subtitle_silence_trimming and missing_api_names:
                audio_file = self._find_audio_file(base_path)
                audio_path = os.path.join(base_dir, audio_file)
                self._ensure_silence_detection(audio_path)

            # 为每个缺失的API执行完整流程
            result_items = []
            configs_to_process = [config for config in active_configs if config.name in missing_api_names]

            for api_config in configs_to_process:
                result = self._process_single_api_complete_workflow(
                    api_config, full_text, words, base_path, service_name, task_folder
                )
                if result:
                    result_items.append(result)

            # 添加已存在的字幕文件信息（严格验证服务名称匹配）
            for api_name, srt_path in existing_subtitles.items():
                try:
                    # 验证字幕文件确实属于当前服务
                    srt_filename = os.path.basename(srt_path)
                    expected_service_pattern = f"-{service_name}-"

                    if expected_service_pattern not in srt_filename:
                        self._log_warning(f"跳过不属于当前服务 {service_name} 的字幕文件: {srt_filename}")
                        continue

                    # 对齐日志不再单独生成文件
                    segments_count = 0
                    try:
                        with open(srt_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        segments_count = content.count('-->')
                    except Exception:
                        segments_count = 0

                    result_items.append({
                        "service_name": service_name,
                        "api_name": api_name,
                        "srt_path": srt_path,
                        "log_path": "",  # 对齐日志不再单独生成
                        "segments": segments_count,
                        "existing": True
                    })
                    self._log_info(f"添加已存在的字幕文件: {api_name} -> {os.path.basename(srt_path)}")
                except Exception as e:
                    self._log_error(f"处理已存在字幕文件 {api_name} 时出错: {e}")

            if not result_items:
                return {"success": False, "error": "所有API都未能生成有效的字幕"}

            self._log_info(f"字幕生成完成，共生成 {len(result_items)} 个字幕文件")

            # 输出匹配统计信息
            self._log_matching_statistics()

            return {
                "success": True,
                "results": result_items
            }

        except Exception as e:
            error_msg = f"字幕生成出错: {e}"
            self._log_error(error_msg)
            logger.error(error_msg, exc_info=True)
            return {"success": False, "error": error_msg}

    def _process_single_api_complete_workflow(self, api_config, full_text: str, words: List[Dict[str, Any]],
                                            base_path: str, service_name: str, task_folder: str) -> Optional[Dict[str, Any]]:
        """单个API的完整处理流程

        Args:
            api_config: API配置对象
            full_text: 完整文本
            words: ASR词汇列表
            base_path: 基础文件路径
            service_name: 服务名称
            task_folder: 任务文件夹路径

        Returns:
            Optional[Dict[str, Any]]: 成功时返回字幕生成结果，失败时返回None
        """
        api_name = api_config.name

        try:
            # 步骤1：LLM调用和分块处理
            self._log_info(f"开始处理 {api_name}...")

            llm_result = self._call_single_api_for_segmentation(
                api_config, full_text, task_folder
            )

            if not llm_result["success"]:
                self._log_error(f"{api_name}: LLM调用失败 - {llm_result.get('error', '未知错误')}")
                return None

            # 步骤2：立即进行字幕生成
            self._log_info(f"{api_name}: LLM调用成功，开始生成字幕...")

            subtitle_result = self._generate_subtitle_from_segments(
                llm_result["content"], words, base_path, service_name, api_name
            )

            if subtitle_result["success"]:
                self._log_info(f"{api_name}: 字幕生成完成 - {os.path.basename(subtitle_result['srt_path'])}")

                return {
                    "service_name": service_name,
                    "api_name": api_name,
                    "srt_path": subtitle_result["srt_path"],
                    "log_path": subtitle_result["log_path"],
                    "segments": subtitle_result["segments"]
                }
            else:
                self._log_error(f"{api_name}: 字幕生成失败 - {subtitle_result.get('error', '未知错误')}")
                return None

        except Exception as e:
            self._log_error(f"{api_name}: 处理异常 - {e}")
            logger.error(f"Error in complete workflow for {api_name}: {e}", exc_info=True)
            return None

    def _call_single_api_for_segmentation(self, config, full_text: str, task_folder: str) -> Dict[str, Any]:
        """调用单个LLM API进行文本分割处理

        向指定的LLM API发送文本分割请求，处理响应并解析分割结果。

        Args:
            config: LLM API配置对象
            full_text: 待分割的完整转录文本
            task_folder: 任务文件夹路径

        Returns:
            Dict[str, Any]: API调用结果，包含成功状态和分割片段
        """
        api_identifier = config.name
        system_prompt = self.get_config('user_llm_system_prompt')

        try:
            # 判断是否需要分块处理
            if self._should_chunk_text(full_text):
                self._log_info(f"{api_identifier}: 启用分块处理")
                processing_result = self._process_chunks_with_validation(
                    full_text, system_prompt, config, api_identifier
                )

                return {
                    "success": processing_result["success"],
                    "content": processing_result.get("result", ""),
                    "api_calls": processing_result.get("api_calls", 0),
                    "success_rate": processing_result.get("success_rate", 0),
                    "chunk_results": processing_result.get("chunk_results", []),
                    "processing_stats": processing_result.get("processing_stats", {})
                }
            else:
                self._log_info(f"{api_identifier}: 直接处理")
                content, api_calls = self._process_single_chunk(
                    full_text, system_prompt, config, api_identifier
                )

                return {
                    "success": bool(content),
                    "content": content or "",
                    "api_calls": api_calls,
                    "success_rate": 1.0 if content else 0.0
                }

        except Exception as e:
            self._log_error(f"{api_identifier}: 处理异常 - {e}")
            return {
                "success": False,
                "content": "",
                "api_calls": 0,
                "error": str(e)
            }

    def _generate_subtitle_from_segments(self, content: str, words: List[Dict[str, Any]],
                                       base_path: str, service_name: str, api_name: str) -> Dict[str, Any]:
        """从LLM结果生成字幕文件

        Args:
            content: LLM处理后的文本内容
            words: ASR词汇列表
            base_path: 基础文件路径
            service_name: 服务名称
            api_name: API名称

        Returns:
            Dict[str, Any]: 包含success、srt_path、log_path（空字符串）、segments、error等字段的结果字典
        """
        try:
            # 构建输出文件路径
            srt_path = f"{base_path}-{service_name}-{api_name}.srt"
            # 对齐日志不再单独生成文件

            # 解析内容获取分段
            segments = [line.strip() for line in content.split('\n') if line.strip()]

            if not segments:
                return {
                    "success": False,
                    "error": "没有有效的文本分段",
                    "srt_path": srt_path,
                    "log_path": "",  # 对齐日志不再单独生成
                    "segments": 0
                }

            # 对齐文本与ASR时间戳
            self._progress(f"对齐 {api_name} 的文本分段...")
            # 从base_path推导项目目录
            project_dir = os.path.dirname(base_path)
            subtitle_entries, log_lines = self._align_segments_with_timestamps(segments, words, project_dir)

            if not subtitle_entries:
                return {
                    "success": False,
                    "error": "未能对齐任何文本分段",
                    "srt_path": srt_path,
                    "log_path": "",  # 对齐日志不再单独生成
                    "segments": 0
                }

            self._log_info(f"{api_name}: 成功对齐 {len(subtitle_entries)} 个文本分段")

            # 处理字幕条目
            self._progress(f"处理 {api_name} 的字幕条目...")
            processed_entries = self._process_subtitle_entries(subtitle_entries)

            # 导出SRT文件
            self._export_srt(processed_entries, srt_path)

            # 对齐日志不再单独保存

            return {
                "success": True,
                "srt_path": srt_path,
                "log_path": "",  # 对齐日志不再单独生成
                "segments": len(processed_entries),
                "alignment_results": (subtitle_entries, log_lines)
            }

        except Exception as e:
            self._log_error(f"生成字幕文件失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "srt_path": srt_path if 'srt_path' in locals() else "",
                "log_path": "",  # 对齐日志不再单独生成
                "segments": 0
            }


    def _find_words_in_timerange(self, start_time: float, end_time: float,
                               asr_words: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """查找时间范围内的ASR词汇"""
        matched_words = []

        for word in asr_words:
            word_start = word.get('start_time', 0)
            word_end = word.get('end_time', 0)

            # 词汇与时间范围有重叠
            if (word_start < end_time and word_end > start_time):
                matched_words.append(word)

        # 按时间排序
        matched_words.sort(key=lambda w: w.get('start_time', 0))
        return matched_words

    def _find_time_gaps_in_words(self, words: List[Dict[str, Any]],
                               max_gap: float = 1.5) -> List[int]:
        """查找词汇间超过阈值的时间间隔分割点

        遍历词汇列表，找出相邻词汇间时间间隔超过指定阈值的位置，
        这些位置将作为字幕条目的分割点。

        Args:
            words: 词汇列表，每个词汇包含start_time和end_time字段
            max_gap: 最大允许的时间间隔阈值（秒），默认1.5秒

        Returns:
            List[int]: 分割点索引列表，表示在这些位置进行分割
        """
        split_points = []

        for i in range(1, len(words)):
            prev_word = words[i-1]
            curr_word = words[i]

            prev_end = prev_word.get('end_time', 0)
            curr_start = curr_word.get('start_time', 0)
            gap = curr_start - prev_end

            if gap > max_gap:
                split_points.append(i)

        return split_points

    def _group_words_by_split_points(self, words: List[Dict[str, Any]],
                                    split_points: List[int]) -> List[List[Dict[str, Any]]]:
        """根据分割点将词汇列表分组

        使用分割点索引将连续的词汇列表分割成多个子组，
        每个子组将对应一个独立的字幕条目。

        Args:
            words: 完整的词汇列表
            split_points: 分割点索引列表

        Returns:
            List[List[Dict[str, Any]]]: 分组后的词汇列表，每个子列表对应一个字幕条目
        """
        if not split_points:
            return [words] if words else []

        word_groups = []
        start_idx = 0

        for split_point in split_points:
            if start_idx < split_point:
                word_groups.append(words[start_idx:split_point])
            start_idx = split_point

        # 添加最后一组词汇
        if start_idx < len(words):
            word_groups.append(words[start_idx:])

        return [group for group in word_groups if group]

    def _find_text_split_positions(self, original_text: str,
                                  word_groups: List[List[Dict[str, Any]]]) -> List[int]:
        """在原始文本中查找对应的分割位置

        根据词汇分组信息，在原始文本中找到对应的分割位置，
        用于将文本按时间间隔进行分割。

        Args:
            original_text: 原始完整文本
            word_groups: 按时间间隔分组的词汇列表

        Returns:
            List[int]: 文本中的分割位置索引列表
        """
        if len(word_groups) <= 1:
            return []

        split_positions = []

        # 遍历每个分组边界，查找对应的文本分割位置
        for i in range(len(word_groups) - 1):
            current_group = word_groups[i]
            next_group = word_groups[i + 1]

            if not current_group or not next_group:
                continue

            # 提取边界词汇用于定位
            last_words = [w.get('word', '').strip() for w in current_group[-3:] if w.get('word', '').strip()]
            next_words = [w.get('word', '').strip() for w in next_group[:3] if w.get('word', '').strip()]

            if not last_words or not next_words:
                continue

            # 清理词汇中的标点符号
            last_word = last_words[-1].rstrip('.,!?;:')
            next_word = next_words[0].lstrip('.,!?;:')

            # 在原始文本中定位最后一个词的位置
            last_word_pos = original_text.find(last_word)
            if last_word_pos != -1:
                # 从最后一个词之后开始搜索下一个词
                search_start = last_word_pos + len(last_word)
                next_word_pos = original_text.find(next_word, search_start)

                if next_word_pos != -1:
                    # 将分割点设置在下一个词的开始位置
                    split_pos = next_word_pos
                    split_positions.append(split_pos)

        return split_positions

    def _split_text_by_positions(self, original_text: str, split_positions: List[int]) -> List[str]:
        """根据指定位置分割文本

        使用分割位置索引将原始文本分割成多个文本片段，
        每个片段对应一个字幕条目。

        Args:
            original_text: 待分割的原始文本
            split_positions: 分割位置索引列表

        Returns:
            List[str]: 分割后的文本片段列表
        """
        if not split_positions:
            return [original_text]

        # 对分割位置进行排序和去重
        sorted_positions = sorted(set(split_positions))

        result_texts = []
        start_pos = 0

        for split_pos in sorted_positions:
            if start_pos < split_pos:
                text_part = original_text[start_pos:split_pos].strip()
                if text_part:
                    result_texts.append(text_part)
            start_pos = split_pos

        # 添加最后一个文本片段
        if start_pos < len(original_text):
            text_part = original_text[start_pos:].strip()
            if text_part:
                result_texts.append(text_part)

        return result_texts

    def _basic_text_reconstruction(self, word_groups: List[List[Dict[str, Any]]]) -> List[str]:
        """从词汇分组重建文本片段

        将分组后的词汇列表重新组合成文本片段，
        用于生成字幕条目的文本内容。

        Args:
            word_groups: 分组后的词汇列表

        Returns:
            List[str]: 重建的文本片段列表
        """
        result_texts = []
        for word_group in word_groups:
            if word_group:
                # 提取有效词汇并重建文本
                words = []
                for w in word_group:
                    word_text = w.get('word', '').strip()
                    if word_text:
                        words.append(word_text)

                if words:
                    # 智能添加空格：如果词汇不以标点符号结尾，则添加空格
                    text_parts = []
                    for i, word in enumerate(words):
                        text_parts.append(word)
                        # 如果不是最后一个词，且当前词不以标点结尾，且下一个词不以标点开头，则添加空格
                        if i < len(words) - 1:
                            current_ends_with_punct = word[-1] in '.,!?;:)]}"\''
                            next_starts_with_punct = words[i+1][0] in '.,!?;:([{"\'('
                            if not current_ends_with_punct and not next_starts_with_punct:
                                text_parts.append(' ')

                    text = ''.join(text_parts).strip()
                    if text:
                        result_texts.append(text)
        return result_texts

    def _handle_double_slash_splitting(self, original_text: str,
                                      word_groups: List[List[Dict[str, Any]]]) -> List[str]:
        """处理双斜杠分隔符分割"""
        # 检查//分隔符数量
        double_slash_count = original_text.count('//')

        # 严格条件检查：只处理//数量=1且分割组数=2的情况
        if double_slash_count != 1 or len(word_groups) != 2:
            return self._basic_text_reconstruction(word_groups)

        # 按//分割原始文本
        text_parts = original_text.split('//')

        if len(text_parts) != 2:
            return self._basic_text_reconstruction(word_groups)

        # 清理每个部分的//符号并去除首尾空格
        result_texts = []
        for i, text_part in enumerate(text_parts):
            cleaned_text = text_part.strip()
            if cleaned_text:
                result_texts.append(cleaned_text)

        return result_texts

    def _handle_punctuation_splitting(self, original_text: str,
                                     word_groups: List[List[Dict[str, Any]]]) -> List[str]:
        """处理标点符号分割"""
        # 首先尝试基于原始文本的智能分割
        split_positions = self._find_text_split_positions(original_text, word_groups)

        if split_positions:
            # 使用原始文本分割
            result_texts = self._split_text_by_positions(original_text, split_positions)

            # 处理标点符号归属
            final_texts = []
            punctuation_chars = set('。！？；：，、.!?;:,…')

            for i, text in enumerate(result_texts):
                if i > 0 and text and final_texts:
                    # 找到开头的连续标点符号
                    leading_punctuation = ""
                    for char in text:
                        if char in punctuation_chars:
                            leading_punctuation += char
                        else:
                            break

                    if leading_punctuation:
                        # 将连续标点符号移到前一个文本块末尾
                        final_texts[-1] += leading_punctuation
                        text = text[len(leading_punctuation):].strip()

                if text:  # 确保文本不为空
                    final_texts.append(text)

            return final_texts
        else:
            # 回退到基础重建方法
            return self._basic_text_reconstruction(word_groups)

    def _smart_split_text_with_formatting(self, original_text: str,
                                         word_groups: List[List[Dict[str, Any]]]) -> List[str]:
        """智能文本分割"""
        if len(word_groups) <= 1:
            return [original_text]

        # 首先处理//分隔符的情况
        if '//' in original_text:
            return self._handle_double_slash_splitting(original_text, word_groups)

        # 处理标点符号的情况
        return self._handle_punctuation_splitting(original_text, word_groups)

    def _split_entry_by_time_gaps(self, start_time: float, end_time: float, text: str,
                                 matched_words: List[Dict[str, Any]],
                                 split_points: List[int]) -> List[Tuple[float, float, str]]:
        """根据时间间隔分割点将字幕条目分割为多个子条目

        处理步骤：
        1. 根据分割点对词汇进行分组
        2. 智能分割文本并保持格式
        3. 为每个分组创建新的字幕条目
        4. 计算精确的时间戳

        Args:
            start_time: 原始条目开始时间
            end_time: 原始条目结束时间
            text: 原始文本内容
            matched_words: 匹配的ASR词汇列表
            split_points: 分割点索引列表

        Returns:
            List[Tuple[float, float, str]]: 分割后的字幕条目列表
        """
        # 根据分割点分组词汇
        word_groups = self._group_words_by_split_points(matched_words, split_points)

        # 智能分割文本
        split_texts = self._smart_split_text_with_formatting(text, word_groups)

        # 创建分割后的条目
        split_entries = []

        for group_idx, (word_group, split_text) in enumerate(zip(word_groups, split_texts)):
            if not word_group or not split_text.strip():
                continue

            # 计算新的时间范围
            new_start = word_group[0].get('start_time', start_time)
            new_end = word_group[-1].get('end_time', end_time)

            split_entries.append((new_start, new_end, split_text))

        return split_entries

    def _process_subtitle_entries(self, entries: List[Tuple[float, float, str]]) -> List[Tuple[float, float, str]]:
        """处理字幕条目的时间间隔分割

        根据ASR词汇间的时间间隔，将过长的字幕条目分割为更短的片段，
        提高字幕的可读性和时间同步精度。

        处理逻辑：
        - 检查时间间隔分割开关
        - 验证ASR词汇数据可用性
        - 对每个条目执行时间间隔分析
        - 在合适的位置分割长条目

        Args:
            entries: 原始字幕条目列表 (开始时间, 结束时间, 文本)

        Returns:
            List[Tuple[float, float, str]]: 处理后的字幕条目列表
        """
        # 检查是否启用时间间隔分割
        if not self.enable_time_gap_splitting:
            self._log_debug("时间间隔分割功能已禁用")
            return entries

        # 如果没有ASR词汇数据，直接返回原始条目
        if not hasattr(self, 'asr_words') or not self.asr_words:
            self._log_warning("没有ASR词汇数据，跳过时间间隔分割")
            return entries

        processed_entries = []

        self._log_debug(f"开始处理 {len(entries)} 个字幕条目的时间间隔分割...")

        for entry_idx, (start_time, end_time, text) in enumerate(entries):
            # 1. 找到这个条目对应的ASR词汇
            matched_words = self._find_words_in_timerange(start_time, end_time, self.asr_words)

            if len(matched_words) <= 1:
                # 词汇太少，无法分割
                processed_entries.append((start_time, end_time, text))
                continue

            # 2. 分析词汇间的时间间隔
            split_points = self._find_time_gaps_in_words(matched_words, self.max_word_gap)

            # 3. 如果有分割点，执行分割
            if split_points:
                split_entries = self._split_entry_by_time_gaps(
                    start_time, end_time, text, matched_words, split_points
                )
                processed_entries.extend(split_entries)
            else:
                # 没有分割点，保持原样
                processed_entries.append((start_time, end_time, text))

        original_count = len(entries)
        processed_count = len(processed_entries)
        self._log_info(f"时间间隔分割完成：{original_count} 个条目 → {processed_count} 个条目")

        # 两轮条目过滤（短时长 + 静音重合）
        filtered_entries = self._apply_two_round_entry_filtering(processed_entries)

        return filtered_entries

    def _export_srt(self, entries: List[Tuple[float, float, str]], output_path: str) -> None:
        """导出SRT文件（集成静音裁剪功能）"""
        # 应用字幕静音裁剪
        trimmed_entries = self._apply_subtitle_silence_trimming(entries)

        with open(output_path, 'w', encoding='utf-8') as f:
            for idx, (start, end, text) in enumerate(trimmed_entries, 1):
                f.write(f"{idx}\n")
                f.write(f"{self._format_timecode(start)} --> {self._format_timecode(end)}\n")
                # 在输出时将//替换为换行符，实现字幕条内的多行显示
                display_text = text.replace('//', '\n')
                f.write(f"{display_text}\n\n")

    def _format_timecode(self, seconds: float) -> str:
        """格式化时间码"""
        # 转换为毫秒进行整数运算，避免浮点数精度问题
        total_ms = int(round(seconds * 1000))

        ms = total_ms % 1000
        total_seconds = total_ms // 1000

        s = total_seconds % 60
        total_minutes = total_seconds // 60

        m = total_minutes % 60
        h = total_minutes // 60

        return f"{h:02d}:{m:02d}:{s:02d},{ms:03d}"

    # ==================== 条目过滤相关方法 ====================

    def _apply_two_round_entry_filtering(self, entries: List[Tuple[float, float, str]]) -> List[Tuple[float, float, str]]:
        """应用两轮条目过滤处理

        第一轮：短时长过滤
        第二轮：静音重合过滤
        """
        self._log_debug("开始两轮条目过滤...")

        # 第一轮：短时长过滤
        first_filtered = self._first_round_entry_filtering(entries)

        # 第二轮：静音重合过滤
        final_filtered = self._second_round_entry_filtering(first_filtered)

        return final_filtered

    def _first_round_entry_filtering(self, entries: List[Tuple[float, float, str]]) -> List[Tuple[float, float, str]]:
        """第一轮条目过滤：短时长过滤

        移除持续时间小于150毫秒的字幕条目，提高字幕可读性。
        """
        self._log_debug("开始第一轮条目过滤...")
        self._log_debug(f"第一轮过滤: 处理{len(entries)}个条目")

        MIN_DURATION = 0.15  # 150毫秒
        filtered_entries = []
        short_duration_count = 0

        for start_time, end_time, text in entries:
            duration = end_time - start_time

            if duration >= MIN_DURATION:
                filtered_entries.append((start_time, end_time, text))
            else:
                short_duration_count += 1
                self._log_debug(f"第一轮-过滤短时长: '{text}' ({self._format_timecode(start_time)} --> {self._format_timecode(end_time)}, 持续时间: {duration:.3f}s)")

        # 输出统计信息
        original_count = len(entries)
        filtered_count = len(filtered_entries)

        if short_duration_count > 0:
            self._log_debug(f"第一轮过滤: 处理{original_count}个, 过滤掉{short_duration_count}个短时长条目")
            self._log_info(f"第一轮过滤完成：{original_count} 个条目 → {filtered_count} 个条目（短时长过滤）")
        else:
            self._log_debug(f"第一轮过滤: 处理{original_count}个条目，无需过滤")

        return filtered_entries

    def _second_round_entry_filtering(self, entries: List[Tuple[float, float, str]]) -> List[Tuple[float, float, str]]:
        """第二轮条目过滤：静音重合过滤

        移除完全落在静音区域内的字幕条目。
        """
        self._log_debug("开始第二轮条目过滤...")
        self._log_debug(f"第二轮过滤: 处理{len(entries)}个条目")

        # 检查静音数据可用性
        if not self._cached_silences:
            self._log_debug("第二轮过滤: 无静音数据，跳过静音重合过滤")
            return entries

        filtered_entries = []
        silence_overlapped_count = 0

        for start_time, end_time, text in entries:
            if self._is_completely_in_silence(start_time, end_time):
                silence_overlapped_count += 1
                self._log_debug(f"第二轮-过滤静音重合: '{text}' ({self._format_timecode(start_time)} --> {self._format_timecode(end_time)})")
            else:
                filtered_entries.append((start_time, end_time, text))

        # 输出统计信息
        original_count = len(entries)
        filtered_count = len(filtered_entries)

        if silence_overlapped_count > 0:
            self._log_debug(f"第二轮过滤: 处理{original_count}个, 过滤掉{silence_overlapped_count}个静音重合条目")
            self._log_info(f"第二轮过滤完成：{original_count} 个条目 → {filtered_count} 个条目（静音重合过滤）")
        else:
            self._log_debug(f"第二轮过滤: 处理{original_count}个条目，无需过滤")

        return filtered_entries

    def _is_completely_in_silence(self, start_time: float, end_time: float) -> bool:
        """判断时间区间是否完全落在某个静音区域内"""
        EPSILON = 1e-6  # 浮点数比较容差

        # 检查有效性
        if end_time - start_time <= EPSILON:
            return False

        # 检查是否完全包含在任一静音区域内
        for silence in self._cached_silences:
            if (silence.start <= start_time + EPSILON and
                end_time <= silence.end + EPSILON):
                return True

        return False

    # ==================== 字幕静音裁剪相关方法 ====================

    def _detect_audio_silences(self, audio_file: str) -> List[SilenceSegment]:
        """
        使用FFmpeg检测音频中的静音段

        Args:
            audio_file: 音频文件路径

        Returns:
            静音段列表，按时间排序
        """
        self._log_info(f"正在检测音频静音段: {audio_file}")

        # 构建FFmpeg命令
        cmd = [
            'ffmpeg',
            '-i', audio_file,
            '-af', f'silencedetect=n={self.noise_threshold}dB:d={self.min_silence_duration}',
            '-f', 'null',
            '-'
        ]

        try:
            # 执行FFmpeg命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=False  # 不自动抛出异常，手动检查
            )

            if result.returncode != 0:
                self._log_warning(f"FFmpeg执行警告 (返回码: {result.returncode})")
                self._log_debug(f"错误信息: {result.stderr}")
                # 继续处理，因为FFmpeg在某些情况下会返回非0但仍然成功

            # 解析静音段信息
            silences = self._parse_silence_output(result.stderr)
            self._log_info(f"检测到 {len(silences)} 个静音段")

            return silences

        except FileNotFoundError:
            raise RuntimeError("未找到FFmpeg，请确保FFmpeg已安装并在PATH中")
        except Exception as e:
            raise RuntimeError(f"FFmpeg执行失败: {e}")

    def _parse_silence_output(self, stderr_output: str) -> List[SilenceSegment]:
        """
        解析FFmpeg silencedetect的输出

        Args:
            stderr_output: FFmpeg的stderr输出

        Returns:
            解析出的静音段列表
        """
        silences = []

        # 提取silence_start和silence_end
        start_pattern = r'silence_start:\s*(\d+\.?\d*)'
        end_pattern = r'silence_end:\s*(\d+\.?\d*)'

        start_matches = re.findall(start_pattern, stderr_output)
        end_matches = re.findall(end_pattern, stderr_output)

        # 处理开头和结尾的特殊情况
        start_times = [float(t) for t in start_matches]
        end_times = [float(t) for t in end_matches]

        # 如果开头就是静音，第一个end没有对应的start
        if len(end_times) > len(start_times):
            start_times.insert(0, 0.0)

        # 如果结尾是静音，最后一个start没有对应的end
        if len(start_times) > len(end_times):
            # 这种情况下，我们无法确定结尾时间，忽略最后一个start
            start_times = start_times[:-1]

        # 组合成静音段
        for start, end in zip(start_times, end_times):
            if end > start:  # 确保时间有效
                silences.append(SilenceSegment(start, end))

        # 按开始时间排序
        silences.sort(key=lambda s: s.start)

        return silences

    def _find_speech_start(self, start_time: float, silences: List[SilenceSegment]) -> float:
        """
        从开始时间向内搜索，找到第一个语音开始点

        Args:
            start_time: 原始开始时间
            silences: 静音段列表

        Returns:
            裁剪后的开始时间
        """
        # 检查start_time是否在某个静音段中
        for silence in silences:
            if silence.start <= start_time <= silence.end:
                # 在静音中，返回静音结束点（语音开始点）
                return silence.end

        # 不在静音中，已经在语音中，保持原时间
        return start_time

    def _find_speech_end(self, end_time: float, silences: List[SilenceSegment]) -> float:
        """
        从结束时间向内搜索，找到最后一个语音结束点

        Args:
            end_time: 原始结束时间
            silences: 静音段列表

        Returns:
            裁剪后的结束时间
        """
        # 检查end_time是否在某个静音段中
        for silence in silences:
            if silence.start <= end_time <= silence.end:
                # 在静音中，返回静音开始点（语音结束点）
                return silence.start

        # 不在静音中，已经在语音中，保持原时间
        return end_time

    def _is_valid_silence_adjustment(self, preprocessed_point: float, trimmed_point: float,
                                    silences: List[SilenceSegment], is_start_point: bool) -> bool:
        """
        验证调整是否来自真正的静音边界

        Args:
            preprocessed_point: 偏移后的时间点
            trimmed_point: 裁剪后的时间点
            silences: 静音段列表
            is_start_point: 是否为开始点

        Returns:
            是否为有效的静音边界调整
        """
        # 查找包含preprocessed_point且持续时间≥150ms的静音段
        containing_silence = None
        for silence in silences:
            silence_duration = silence.end - silence.start
            if (silence.start <= preprocessed_point <= silence.end and
                silence_duration >= 0.15):  # 要求静音段≥150ms
                containing_silence = silence
                break

        # 如果不在静音段内，不应该有调整
        if containing_silence is None:
            return False

        # 验证调整是否到了正确的静音边界
        if is_start_point:
            # 开始点应该调整到静音段结束点（语音开始点）
            expected_point = containing_silence.end
        else:
            # 结束点应该调整到静音段开始点（语音结束点）
            expected_point = containing_silence.start

        # 检查调整结果是否符合预期
        return abs(trimmed_point - expected_point) < 0.001

    def _validate_trimming_result(self, original_entry: Tuple[float, float, str],
                                 preprocessed_entry: Tuple[float, float, str],
                                 trimmed_entry: Tuple[float, float, str],
                                 silences: List[SilenceSegment]) -> bool:
        """
        验证裁剪结果是否基于有效的静音边界调整

        Args:
            original_entry: 原始字幕条目
            preprocessed_entry: 偏移后的字幕条目
            trimmed_entry: 裁剪后的字幕条目
            silences: 静音段列表

        Returns:
            裁剪结果是否有效
        """
        original_start, original_end, _ = original_entry
        preprocessed_start, preprocessed_end, _ = preprocessed_entry
        trimmed_start, trimmed_end, _ = trimmed_entry

        # 检查开始点是否有变化
        start_changed = abs(trimmed_start - preprocessed_start) > 0.001
        if start_changed:
            # 验证开始点的调整是否有效
            if not self._is_valid_silence_adjustment(preprocessed_start, trimmed_start, silences, True):
                return False

        # 检查结束点是否有变化
        end_changed = abs(trimmed_end - preprocessed_end) > 0.001
        if end_changed:
            # 验证结束点的调整是否有效
            if not self._is_valid_silence_adjustment(preprocessed_end, trimmed_end, silences, False):
                return False

        return True

    # ========== 新补丁：重叠度检查机制 ==========
    # 用途：确保调整后的条目与原始条目有合理重叠，防止条目被完全移走
    # 添加时间：2025-01-XX
    # 可调参数：overlap_ratio >= 0.2 表示重叠部分至少占原始时长的20%
    def _has_reasonable_overlap(self, original: Tuple[float, float, str], trimmed: Tuple[float, float, str]) -> bool:
        """检查调整后的条目与原始条目是否有合理重叠"""
        # 计算重叠区间
        overlap_start = max(original[0], trimmed[0])
        overlap_end = min(original[1], trimmed[1])

        if overlap_end <= overlap_start:
            return False  # 无重叠，直接拒绝

        overlap_duration = overlap_end - overlap_start
        original_duration = original[1] - original[0]

        if original_duration <= 0:
            return False  # 防止除零错误

        # 重叠部分至少占原始时长的20%
        overlap_ratio = overlap_duration / original_duration
        return overlap_ratio >= 0.2
    # ========== 新补丁结束 ==========

    def _apply_start_offset(self, entries: List[Tuple[float, float, str]], offset_ms: int, direction: str) -> List[Tuple[float, float, str]]:
        """只对开始时间应用偏移"""
        preprocessed = []
        offset_seconds = (offset_ms * (-1 if direction == "left" else 1)) / 1000.0

        for start, end, text in entries:
            new_start = max(0, start + offset_seconds)

            # 确保有效性：结束时间必须大于开始时间至少100ms
            if end > new_start + 0.1:
                preprocessed.append((new_start, end, text))
            else:
                # 偏移后无效，保持原样
                preprocessed.append((start, end, text))

        return preprocessed

    def _apply_end_offset(self, entries: List[Tuple[float, float, str]], offset_ms: int, direction: str) -> List[Tuple[float, float, str]]:
        """只对结束时间应用偏移"""
        preprocessed = []
        offset_seconds = (offset_ms * (-1 if direction == "left" else 1)) / 1000.0

        for start, end, text in entries:
            new_end = end + offset_seconds

            # 确保有效性：结束时间必须大于开始时间至少100ms
            if new_end > start + 0.1:
                preprocessed.append((start, new_end, text))
            else:
                # 偏移后无效，保持原样
                preprocessed.append((start, end, text))

        return preprocessed
    def _find_best_start_offset(self, entries: List[Tuple[float, float, str]], direction: str, round_name: str) -> List[Tuple[float, float, str]]:
        """阶段1：真正的渐进式条目级步进逻辑"""
        start_offsets = [100, 200]  # ms - 移除300ms档位

        # 初始化结果数组和处理状态
        result = list(entries)  # 复制原始条目作为默认结果
        unprocessed_mask = [True] * len(entries)  # 标记未处理的条目
        total_valid_adjustments = 0

        for offset_ms in start_offsets:
            # 构建当前轮次的处理列表
            current_entries = []
            current_indices = []

            for i, is_unprocessed in enumerate(unprocessed_mask):
                if is_unprocessed:
                    current_entries.append(entries[i])
                    current_indices.append(i)

            if not current_entries:
                self._log_debug(f"{round_name}-开始时间: {offset_ms}ms偏移跳过（所有条目已处理）")
                break

            # 对未处理条目应用偏移和裁剪
            preprocessed = self._apply_start_offset(current_entries, offset_ms, direction)
            trimmed, _ = self._trim_silence_from_entries(
                self._cached_silences, preprocessed, calculate_trimmed_time=False)

            # 验证和更新结果
            current_valid_adjustments = 0

            for j, original_idx in enumerate(current_indices):
                original_entry = entries[original_idx]
                preprocessed_entry = preprocessed[j]
                trimmed_entry = trimmed[j]

                # 检查变化和验证
                if (abs(trimmed_entry[0] - preprocessed_entry[0]) > 0.001 or
                    abs(trimmed_entry[1] - preprocessed_entry[1]) > 0.001):

                    # ========== 新补丁：在阶段1中添加重叠度检查 ==========
                    # 原有验证 + 新增重叠度检查，防止条目被移到不合理位置
                    if (self._validate_trimming_result(original_entry, preprocessed_entry,
                                                     trimmed_entry, self._cached_silences) and
                        self._has_reasonable_overlap(original_entry, trimmed_entry)):  # 新补丁：重叠度检查
                        # ========== 新补丁结束 ==========

                        # 成功！更新结果并标记为已处理
                        result[original_idx] = trimmed_entry
                        unprocessed_mask[original_idx] = False
                        current_valid_adjustments += 1

            total_valid_adjustments += current_valid_adjustments

            # 日志输出
            remaining_count = sum(unprocessed_mask)
            if current_valid_adjustments > 0:
                self._log_debug(f"{round_name}-开始时间: 在{offset_ms}ms偏移找到{current_valid_adjustments}个有效调整，剩余{remaining_count}个条目")
            else:
                self._log_debug(f"{round_name}-开始时间: {offset_ms}ms偏移无有效调整，剩余{remaining_count}个条目")

        # 最终统计
        final_remaining = sum(unprocessed_mask)
        self._log_debug(f"{round_name}-开始时间: 总计处理{total_valid_adjustments}个条目，{final_remaining}个条目保持原样")

        return result

    def _find_best_end_offset(self, entries: List[Tuple[float, float, str]], direction: str, round_name: str) -> List[Tuple[float, float, str]]:
        """阶段2：真正的渐进式条目级步进逻辑"""
        end_offsets = [100, 200]  # ms - 移除300ms档位

        # 初始化结果数组和处理状态
        result = list(entries)  # 复制阶段1的结果作为默认结果
        unprocessed_mask = [True] * len(entries)  # 标记未处理的条目
        total_valid_adjustments = 0

        for offset_ms in end_offsets:
            # 构建当前轮次的处理列表
            current_entries = []
            current_indices = []

            for i, is_unprocessed in enumerate(unprocessed_mask):
                if is_unprocessed:
                    current_entries.append(entries[i])  # 注意：这里的entries是阶段1的输出
                    current_indices.append(i)

            if not current_entries:
                self._log_debug(f"{round_name}-结束时间: {offset_ms}ms偏移跳过（所有条目已处理）")
                break

            # 对未处理条目应用偏移和裁剪
            preprocessed = self._apply_end_offset(current_entries, offset_ms, direction)
            trimmed, _ = self._trim_silence_from_entries(
                self._cached_silences, preprocessed, calculate_trimmed_time=False)

            # 验证和更新结果
            current_valid_adjustments = 0

            for j, original_idx in enumerate(current_indices):
                original_entry = entries[original_idx]  # 注意：这里的original是阶段1的输出
                preprocessed_entry = preprocessed[j]
                trimmed_entry = trimmed[j]

                # 检查变化和验证
                if (abs(trimmed_entry[0] - preprocessed_entry[0]) > 0.001 or
                    abs(trimmed_entry[1] - preprocessed_entry[1]) > 0.001):

                    # ========== 新补丁：在阶段2中添加重叠度检查 ==========
                    # 原有验证 + 新增重叠度检查，防止条目被移到不合理位置
                    if (self._validate_trimming_result(original_entry, preprocessed_entry,
                                                     trimmed_entry, self._cached_silences) and
                        self._has_reasonable_overlap(original_entry, trimmed_entry)):  # 新补丁：重叠度检查
                        # ========== 新补丁结束 ==========

                        # 成功！更新结果并标记为已处理
                        result[original_idx] = trimmed_entry
                        unprocessed_mask[original_idx] = False
                        current_valid_adjustments += 1

            total_valid_adjustments += current_valid_adjustments

            # 日志输出
            remaining_count = sum(unprocessed_mask)
            if current_valid_adjustments > 0:
                self._log_debug(f"{round_name}-结束时间: 在{offset_ms}ms偏移找到{current_valid_adjustments}个有效调整，剩余{remaining_count}个条目")
            else:
                self._log_debug(f"{round_name}-结束时间: {offset_ms}ms偏移无有效调整，剩余{remaining_count}个条目")

        # 最终统计
        final_remaining = sum(unprocessed_mask)
        self._log_debug(f"{round_name}-结束时间: 总计处理{total_valid_adjustments}个条目，{final_remaining}个条目保持原样")

        return result

    def _progressive_independent_trimming(self, entries: List[Tuple[float, float, str]], direction: str, round_name: str) -> List[Tuple[float, float, str]]:
        """分阶段独立步进调整"""

        # 阶段1：找到最佳开始时间偏移
        best_start_result = self._find_best_start_offset(entries, direction, round_name)

        # 阶段2：基于最佳开始偏移，找到最佳结束时间偏移
        final_result = self._find_best_end_offset(best_start_result, direction, round_name)

        return final_result
    def _trim_silence_from_entries(self, silences: List[SilenceSegment],
                                  entries: List[Tuple[float, float, str]],
                                  calculate_trimmed_time: bool = True) -> Tuple[List[Tuple[float, float, str]], float]:
        """
        裁剪字幕时间戳中的静音区域（适配tuple格式）

        Args:
            silences: 静音段列表
            entries: 原始字幕条目列表
            calculate_trimmed_time: 是否计算裁剪时间（默认True，第二轮和第三轮应设为False）

        Returns:
            Tuple[裁剪后的字幕条目列表, 裁剪的总时间]
        """
        self._log_debug("正在裁剪字幕静音区域...")

        trimmed_entries = []
        total_trimmed_time = 0.0

        for i, (start_time, end_time, text) in enumerate(entries):
            # 前向搜索：找到开始时间之后第一个语音开始点
            new_start = self._find_speech_start(start_time, silences)

            # 后向搜索：找到结束时间之前最后一个语音结束点
            new_end = self._find_speech_end(end_time, silences)

            # 确保新的时间戳有效
            if new_end <= new_start:
                # 如果裁剪后时间无效，保持原时间
                new_start = start_time
                new_end = end_time
            else:
                # 只有在需要时才计算节省的时间（避免基于虚假数据的计算）
                if calculate_trimmed_time:
                    original_duration = end_time - start_time
                    new_duration = new_end - new_start
                    trimmed = original_duration - new_duration
                    total_trimmed_time += trimmed

            # 创建裁剪后的条目
            trimmed_entries.append((new_start, new_end, text))

        return trimmed_entries, total_trimmed_time

    def _second_round_silence_trimming(self, entries: List[Tuple[float, float, str]]) -> List[Tuple[float, float, str]]:
        """
        第二轮静音裁剪：分阶段独立步进向左偏移试探策略

        1. 阶段1：只调整开始时间，渐进向左偏移(100ms→200ms)
        2. 阶段2：基于阶段1结果，只调整结束时间，渐进向左偏移
        3. 每个阶段找到有效调整就停止，避免过度调整
        4. 使用严格的静音边界验证逻辑

        Args:
            entries: 第一轮裁剪后的字幕条目列表

        Returns:
            第二轮处理后的字幕条目列表
        """
        self._log_debug("开始第二轮静音裁剪...")
        self._log_debug(f"第二轮裁剪: 处理{len(entries)}个条目")

        # 使用分阶段独立步进调整
        result = self._progressive_independent_trimming(entries, direction="left", round_name="第二轮")

        # 统计总体效果
        total_changes = sum(1 for i in range(len(entries))
                           if (abs(result[i][0] - entries[i][0]) > 0.001 or
                               abs(result[i][1] - entries[i][1]) > 0.001))

        self._log_debug(f"第二轮裁剪: 处理{len(entries)}个, 总计调整{total_changes}个条目")
        return result

    def _third_round_silence_trimming(self, entries: List[Tuple[float, float, str]]) -> List[Tuple[float, float, str]]:
        """
        第三轮静音裁剪：分阶段独立步进向右偏移试探策略

        1. 阶段1：只调整开始时间，渐进向右偏移(100ms→200ms)
        2. 阶段2：基于阶段1结果，只调整结束时间，渐进向右偏移
        3. 每个阶段找到有效调整就停止，避免过度调整
        4. 使用严格的静音边界验证逻辑

        Args:
            entries: 第二轮裁剪后的字幕条目列表

        Returns:
            第三轮处理后的字幕条目列表
        """
        self._log_debug("开始第三轮静音裁剪...")
        self._log_debug(f"第三轮裁剪: 处理{len(entries)}个条目")

        # 使用分阶段独立步进调整
        result = self._progressive_independent_trimming(entries, direction="right", round_name="第三轮")

        # 统计总体效果
        total_changes = sum(1 for i in range(len(entries))
                           if (abs(result[i][0] - entries[i][0]) > 0.001 or
                               abs(result[i][1] - entries[i][1]) > 0.001))

        self._log_debug(f"第三轮裁剪: 处理{len(entries)}个, 总计调整{total_changes}个条目")
        return result

    def _expand_tuple_entries(self, entries: List[Tuple[float, float, str]], expand_ms: int) -> List[Tuple[float, float, str]]:
        """
        扩充字幕显示时间（适配tuple格式）

        包含两个阶段：
        1. 标准扩充：所有字幕统一扩充指定时间
        2. 智能扩充：将短字幕扩充到目标时长

        Args:
            entries: 字幕条目列表
            expand_ms: 标准扩充时间（毫秒），开始时间提前，结束时间延后

        Returns:
            扩充后的字幕条目列表
        """
        if expand_ms <= 0 and self.target_duration_ms <= 0:
            return entries

        # 第一阶段：标准扩充
        standard_expanded = self._apply_standard_expansion(entries, expand_ms)

        # 第二阶段：智能短字幕扩充
        if self.target_duration_ms > 0:
            final_expanded = self._apply_smart_expansion(standard_expanded)
        else:
            final_expanded = standard_expanded

        return final_expanded

    def _apply_standard_expansion(self, entries: List[Tuple[float, float, str]], expand_ms: int) -> List[Tuple[float, float, str]]:
        """
        标准扩充：所有字幕统一扩充指定时间（原有逻辑）

        Args:
            entries: 字幕条目列表
            expand_ms: 扩充时间（毫秒），开始时间提前，结束时间延后

        Returns:
            标准扩充后的字幕条目列表
        """
        if expand_ms <= 0:
            return entries

        expand_seconds = expand_ms / 1000.0

        # 统计变量
        original_count = len(entries)
        fully_expanded = 0
        partially_expanded = 0
        total_time_added = 0.0

        # 记录原始时长
        original_durations = [(end_time - start_time) for start_time, end_time, _ in entries]

        # 创建新的字幕列表（避免修改原列表）
        expanded_entries = []

        # 第一步：扩充所有字幕
        for start_time, end_time, text in entries:
            new_start = max(0, start_time - expand_seconds)  # 不能小于0
            new_end = end_time + expand_seconds
            expanded_entries.append((new_start, new_end, text))

        # 第二步：解决重叠冲突
        expanded_entries, conflicts_resolved = self._resolve_expansion_conflicts(expanded_entries)

        # 计算统计信息
        for i, (original_duration, (start_time, end_time, _)) in enumerate(zip(original_durations, expanded_entries)):
            new_duration = end_time - start_time
            time_added = new_duration - original_duration
            total_time_added += time_added

            if abs(time_added - 2 * expand_seconds) < 0.001:
                fully_expanded += 1
            elif time_added > 0.001:
                partially_expanded += 1

        # 输出统计信息（统一的日志顺序：统计信息 → 冲突解决）
        self._log_info(f"标准扩充: 增加显示 {total_time_added:.3f} 秒")
        self._log_debug(f"标准扩充详情: 完全{fully_expanded}个, 部分{partially_expanded}个")
        if conflicts_resolved > 0:
            self._log_debug(f"解决扩充冲突: {conflicts_resolved}个")

        return expanded_entries

    def _resolve_expansion_conflicts(self, entries: List[Tuple[float, float, str]]) -> Tuple[List[Tuple[float, float, str]], int]:
        """
        解决扩充后的重叠冲突

        Args:
            entries: 可能存在重叠的字幕条目列表

        Returns:
            Tuple[List[Tuple[float, float, str]], int]: (解决冲突后的字幕条目列表, 解决的冲突数量)
        """
        if len(entries) <= 1:
            return entries, 0

        resolved_entries = list(entries)  # 创建副本
        conflicts_resolved = 0

        for i in range(len(resolved_entries) - 1):
            current_start, current_end, current_text = resolved_entries[i]
            next_start, next_end, next_text = resolved_entries[i + 1]

            if current_end >= next_start:  # 检测重叠（包括相同时间戳）
                conflicts_resolved += 1
                # 在重叠区域的中点分割，并添加1ms间隔避免相同时间戳
                midpoint = (current_end + next_start) / 2
                gap = 0.001  # 1ms间隔

                # 调整两个字幕的边界，确保有间隔
                resolved_entries[i] = (current_start, midpoint - gap/2, current_text)
                resolved_entries[i+1] = (midpoint + gap/2, next_end, next_text)

        return resolved_entries, conflicts_resolved

    def _apply_smart_expansion(self, entries: List[Tuple[float, float, str]]) -> List[Tuple[float, float, str]]:
        """
        智能短字幕扩充：将短字幕扩充到目标时长

        对于持续时间小于target_duration_ms的字幕，根据周围可用空间
        智能地扩充到目标时长，优先保持左右对称分配。

        修复：使用动态空间计算，基于已扩充的状态计算可用空间

        Args:
            entries: 标准扩充后的字幕条目列表

        Returns:
            智能扩充后的字幕条目列表
        """
        target_seconds = self.target_duration_ms / 1000.0

        # 关键修复：使用工作副本，支持动态空间计算
        smart_expanded = list(entries)  # 创建工作副本
        stats = {
            'processed': 0,
            'fully_expanded': 0,
            'partially_expanded': 0,
            'total_time_added': 0.0
        }

        for i, (start_time, end_time, text) in enumerate(entries):
            current_duration = end_time - start_time

            # 判断是否需要智能扩充
            if current_duration >= target_seconds:
                continue  # 工作副本中已经是正确的条目

            # 计算需要扩充的时间
            needed_expansion = target_seconds - current_duration

            # 关键修复：基于当前已扩充的状态计算可用空间
            left_space = self._get_left_available_space(smart_expanded, i)
            right_space = self._get_right_available_space(smart_expanded, i)
            total_space = left_space + right_space

            # 执行智能分配
            if total_space >= needed_expansion:
                # 空间充足，优先对称分配
                left_expand, right_expand = self._calculate_symmetric_expansion(
                    needed_expansion, left_space, right_space
                )
                stats['fully_expanded'] += 1
            else:
                # 空间不足，最大化利用
                left_expand, right_expand = self._calculate_maximum_expansion(
                    left_space, right_space
                )
                if total_space > 0:  # 只有在有空间时才算部分扩充
                    stats['partially_expanded'] += 1

            # 应用扩充并立即更新工作副本
            if left_expand > 0 or right_expand > 0:
                safety_gap = 0.001  # 1ms安全间隔
                new_start = max(0, start_time - left_expand)
                new_end = end_time + right_expand

                # 检查右边界，确保不会与下一个字幕产生边界重合
                if i < len(smart_expanded) - 1:
                    next_start = smart_expanded[i + 1][0]
                    if new_end >= next_start - safety_gap:
                        new_end = next_start - safety_gap
                        # 如果安全间隔导致时长过短，调整左边界补偿
                        if new_end <= new_start:
                            new_end = new_start + 0.001  # 最小1ms时长

                # 检查左边界，确保不会与前一个字幕产生边界重合
                if i > 0:
                    prev_end = smart_expanded[i - 1][1]
                    if new_start <= prev_end + safety_gap:
                        new_start = prev_end + safety_gap
                        # 如果安全间隔导致时长过短，调整右边界补偿
                        if new_start >= new_end:
                            new_end = new_start + 0.001  # 最小1ms时长

                smart_expanded[i] = (new_start, new_end, text)  # 立即更新工作副本
                stats['processed'] += 1
                stats['total_time_added'] += (new_end - end_time) + (start_time - new_start)

        # 解决智能扩充可能产生的新冲突（作为安全网保留）
        conflicts_resolved = 0
        if stats['processed'] > 0:
            smart_expanded, conflicts_resolved = self._resolve_expansion_conflicts(smart_expanded)

        # 输出统计信息（统一的日志顺序：统计信息 → 冲突解决）
        self._log_smart_expansion_stats(stats)
        if conflicts_resolved > 0:
            self._log_debug(f"解决扩充冲突: {conflicts_resolved}个")

        return smart_expanded

    def _get_left_available_space(self, entries: List[Tuple[float, float, str]], current_index: int) -> float:
        """
        计算指定条目左侧的可用空间

        Args:
            entries: 字幕条目列表
            current_index: 当前条目的索引

        Returns:
            float: 左侧可用空间（秒）
        """
        if current_index == 0:
            # 第一个条目，可用空间是到时间0的距离
            return entries[current_index][0]

        prev_end = entries[current_index - 1][1]
        current_start = entries[current_index][0]
        return max(0, current_start - prev_end)

    def _get_right_available_space(self, entries: List[Tuple[float, float, str]], current_index: int) -> float:
        """
        计算指定条目右侧的可用空间

        Args:
            entries: 字幕条目列表
            current_index: 当前条目的索引

        Returns:
            float: 右侧可用空间（秒），最后一个条目返回较大值
        """
        if current_index == len(entries) - 1:
            # 最后一个条目，理论上可以无限扩展，但设置一个合理上限
            return 10.0  # 10秒的合理上限

        current_end = entries[current_index][1]
        next_start = entries[current_index + 1][0]
        return max(0, next_start - current_end)

    def _calculate_symmetric_expansion(self, needed_expansion: float, left_space: float, right_space: float) -> Tuple[float, float]:
        """
        对称分配扩充时间（空间充足时使用）

        优先保持左右对称，当一侧空间不足时，将剩余时间分配给另一侧，
        左侧优先获得额外时间。

        Args:
            needed_expansion: 需要扩充的总时间（秒）
            left_space: 左侧可用空间（秒）
            right_space: 右侧可用空间（秒）

        Returns:
            Tuple[float, float]: (左侧扩充时间, 右侧扩充时间)
        """
        ideal_each = needed_expansion / 2

        left_expand = min(left_space, ideal_each)
        right_expand = min(right_space, ideal_each)

        # 处理剩余时间，左侧优先
        remaining = needed_expansion - left_expand - right_expand
        if remaining > 0:
            if left_space > left_expand:
                additional_left = min(remaining, left_space - left_expand)
                left_expand += additional_left
                remaining -= additional_left

            if remaining > 0 and right_space > right_expand:
                right_expand += min(remaining, right_space - right_expand)

        return left_expand, right_expand

    def _calculate_maximum_expansion(self, left_space: float, right_space: float) -> Tuple[float, float]:
        """
        最大化利用扩充时间（空间不足时使用）

        在总空间不足以达到目标时长的情况下，尽可能对称地
        利用所有可用空间。

        Args:
            left_space: 左侧可用空间（秒）
            right_space: 右侧可用空间（秒）

        Returns:
            Tuple[float, float]: (左侧扩充时间, 右侧扩充时间)
        """
        total_space = left_space + right_space
        if total_space <= 0:
            return 0, 0

        # 按比例分配，但保持尽可能对称
        ideal_each = total_space / 2
        left_expand = min(left_space, ideal_each)
        right_expand = min(right_space, ideal_each)

        # 将一侧的剩余空间分配给另一侧
        if left_expand < ideal_each and right_space > right_expand:
            right_expand = min(right_space, total_space - left_expand)
        elif right_expand < ideal_each and left_space > left_expand:
            left_expand = min(left_space, total_space - right_expand)

        return left_expand, right_expand

    def _log_smart_expansion_stats(self, stats: Dict[str, Any]) -> None:
        """
        记录智能扩充的统计信息

        Args:
            stats: 包含统计数据的字典
        """
        if stats['processed'] > 0:
            self._log_info(f"智能扩充: 处理{stats['processed']}个短字幕, 增加显示 {stats['total_time_added']:.3f} 秒")
            self._log_debug(f"智能扩充详情: 完全{stats['fully_expanded']}个, 部分{stats['partially_expanded']}个, 目标{self.target_duration_ms}ms")
        else:
            self._log_debug(f"智能扩充: 无需处理的短字幕（目标{self.target_duration_ms}ms）")

    def _bridge_tuple_entries(self, entries: List[Tuple[float, float, str]], bridge_ms: int) -> List[Tuple[float, float, str]]:
        """
        桥接字幕间的短间隔（适配tuple格式）

        Args:
            entries: 字幕条目列表
            bridge_ms: 桥接阈值（毫秒），小于此值的间隔将被桥接

        Returns:
            桥接后的字幕条目列表
        """
        if bridge_ms <= 0:
            return entries

        bridge_seconds = bridge_ms / 1000.0
        bridged_count = 0
        total_time_bridged = 0.0

        # 创建新列表避免修改原列表
        bridged_entries = []
        # 创建可修改的副本
        working_entries = list(entries)

        for i, (start_time, end_time, text) in enumerate(working_entries):
            if i == len(working_entries) - 1:
                # 最后一个字幕，直接添加
                bridged_entries.append((start_time, end_time, text))
            else:
                current_start, current_end, current_text = start_time, end_time, text
                next_start, next_end, next_text = working_entries[i + 1]

                # 计算间隔
                gap = next_start - current_end

                if 0.002 < gap <= bridge_seconds:  # 2ms以下不桥接
                    # 需要桥接：在间隔中点分割
                    midpoint = (current_end + next_start) / 2

                    # 创建桥接后的当前字幕
                    bridged_entries.append((current_start, midpoint, current_text))

                    # 修改下一个字幕的开始时间（在下次循环中处理）
                    working_entries[i + 1] = (midpoint, next_end, next_text)

                    # 统计信息
                    bridged_count += 1
                    total_time_bridged += gap

                else:
                    # 不需要桥接，直接添加
                    bridged_entries.append((current_start, current_end, current_text))

        # 输出统计信息
        self._log_info(f"桥接处理: 填补间隔 {total_time_bridged:.3f} 秒")
        self._log_debug(f"桥接详情: 处理{bridged_count}个间隔, 阈值{bridge_ms}ms")

        return bridged_entries

    def _ensure_silence_detection(self, audio_path: str):
        """确保静音检测已完成并缓存"""
        if (self._cached_audio_path != audio_path or
            self._cached_silences is None):
            self._progress("正在分析音频静音段...")
            self._cached_silences = self._detect_audio_silences(audio_path)
            self._cached_audio_path = audio_path
            self._log_info(f"静音检测完成，缓存结果用于后续处理")

    def _apply_subtitle_silence_trimming(self, entries: List[Tuple[float, float, str]]) -> List[Tuple[float, float, str]]:
        """应用字幕静音裁剪处理"""
        if not self.enable_subtitle_silence_trimming or self._cached_silences is None:
            return entries

        try:
            self._progress("正在裁剪字幕静音区域...")

            # 执行五阶段处理：第一轮裁剪→第二轮裁剪→第三轮裁剪→扩充→桥接
            first_trimmed, trimmed_time = self._trim_silence_from_entries(self._cached_silences, entries)
            self._log_debug(f"第一轮裁剪: 处理{len(entries)}个, 去除静音{trimmed_time:.3f}秒")
            second_trimmed = self._second_round_silence_trimming(first_trimmed)
            trimmed = self._third_round_silence_trimming(second_trimmed)

            if self.expand_ms > 0:
                trimmed = self._expand_tuple_entries(trimmed, self.expand_ms)

            if self.bridge_ms > 0:
                trimmed = self._bridge_tuple_entries(trimmed, self.bridge_ms)

            return trimmed

        except Exception as e:
            self._log_warning(f"字幕静音裁剪失败，使用原始字幕: {e}")
            return entries


# 向后兼容别名
LLMSubtitleService = SubtitleLLMService
