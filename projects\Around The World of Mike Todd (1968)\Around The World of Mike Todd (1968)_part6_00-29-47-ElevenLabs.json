{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754281589", "text": "At 11:00, I saw it was completely hopeless. And I saw that mistake. And so I asked this guy, I said, \"How does so-and-so and so get to so-and-so?\" And he took 12 or 20 or 30 minutes, and he explained it to me. This guy says, \"I've been making pictures for so-and-so and so-and-so and so years.\" And then he said the one thing that nobody should ever say to me. He said, \"Why don't you do it yourself?\" I did. . That's <PERSON>, the young director I got to take over. He did a great job. . We shot in every country that we show in Around the World. My friend, the King of Siam, loaned me the royal barge. He once wrote some songs for one of my cultural achievements, Peep Show. In Hollywood, we shot this sequence where <PERSON><PERSON> makes the last leg of his trip from New York to Liverpool. He dismantles the ship and burns it for fuel. My best friend, a good director was watching, and he said, \"What a beautiful miniature. Look at that smoke, and so-and-so and so-and-so. And isn't it great?\" Well, it was all right, but it's late in the picture and everything we do in the picture is really real. When I say late in the picture, just about the last real big production number before we wind up the story. This whole sequence, now I looked at it, I didn't like it, and it worried me. So I took a loss, and I decided to buy a boat. I made a side-wheeler out of it, and took it out in the ocean and dismantled it, and it's for real. We're coming. Over. Barge to the Henrietta, we're ready. Come right to us. Come full steam ahead. We're coming in on everything you say. Over. I gave you the word right now, \"Come on ahead.\" Is that clear? Very clear. Thank you. Very good, Barge to the Henrietta. That's it. Now keep going. Keep going and make, uh, and, and get out, get around in front of us. When you get in front of the cameras, swing all the way around. This is the, uh, sharp turn. We want a lot of activity along. Okay. Over and out. We started in August and finished the end of December. Then came little things like scoring and cutting and piecing together. But I looked at the prologue and it was phony. I threw it away and spent an extra half million on a whole new prologue just before we opened the picture.", "words": [{"text": "At", "start": 1.979, "end": 2.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.099, "end": 2.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "11:00,", "start": 2.159, "end": 2.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.739, "end": 3.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 3.759, "end": 4.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.039, "end": 4.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "saw", "start": 4.079, "end": 4.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.359, "end": 4.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 4.42, "end": 4.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.559, "end": 4.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 4.579, "end": 4.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.739, "end": 4.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "completely", "start": 4.819, "end": 5.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.539, "end": 5.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hopeless.", "start": 5.619, "end": 6.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.279, "end": 7.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 7.319, "end": 7.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.439, "end": 7.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 7.519, "end": 7.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.579, "end": 7.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "saw", "start": 7.579, "end": 7.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.759, "end": 7.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 7.779, "end": 7.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.899, "end": 7.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mistake.", "start": 7.919, "end": 8.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.44, "end": 9.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 9.46, "end": 9.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.559, "end": 9.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so", "start": 9.599, "end": 9.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.719, "end": 9.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 9.739, "end": 9.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.84, "end": 9.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "asked", "start": 9.859, "end": 10.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.079, "end": 10.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "this", "start": 10.099, "end": 10.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.279, "end": 10.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "guy,", "start": 10.3, "end": 10.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.679, "end": 11.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 11.259, "end": 11.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.379, "end": 11.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "said,", "start": 11.399, "end": 11.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.559, "end": 11.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "\"How", "start": 11.559, "end": 11.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.759, "end": 11.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "does", "start": 11.779, "end": 11.98, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.98, "end": 12.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so-and-so", "start": 12.0, "end": 12.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.519, "end": 12.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 12.559, "end": 12.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.64, "end": 12.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so", "start": 12.719, "end": 12.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.92, "end": 12.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "get", "start": 12.96, "end": 13.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.139, "end": 13.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 13.139, "end": 13.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.279, "end": 13.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so-and-so?\"", "start": 13.319, "end": 14.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.12, "end": 14.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 14.799, "end": 14.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.94, "end": 14.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 14.96, "end": 15.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.079, "end": 15.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "took", "start": 15.079, "end": 15.26, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.26, "end": 15.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "12", "start": 15.339, "end": 15.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.859, "end": 15.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "or", "start": 15.899, "end": 16.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.0, "end": 16.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "20", "start": 16.119, "end": 16.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.459, "end": 16.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "or", "start": 16.459, "end": 16.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.579, "end": 16.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "30", "start": 16.699, "end": 16.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.94, "end": 16.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "minutes,", "start": 16.959, "end": 17.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.299, "end": 17.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 17.6, "end": 17.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.699, "end": 17.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 17.699, "end": 17.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.799, "end": 17.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "explained", "start": 17.84, "end": 18.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.259, "end": 18.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 18.26, "end": 18.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.359, "end": 18.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 18.379, "end": 18.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.44, "end": 18.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me.", "start": 18.5, "end": 18.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.7, "end": 20.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "This", "start": 20.439, "end": 20.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.619, "end": 20.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "guy", "start": 20.639, "end": 20.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.819, "end": 20.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "says,", "start": 20.899, "end": 21.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.219, "end": 21.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "\"I've", "start": 21.219, "end": 22.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.159, "end": 22.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "been", "start": 22.18, "end": 22.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.299, "end": 22.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "making", "start": 22.299, "end": 22.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.559, "end": 22.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pictures", "start": 22.619, "end": 22.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.899, "end": 22.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 22.92, "end": 23.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.039, "end": 23.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so-and-so", "start": 23.059, "end": 23.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.5, "end": 23.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 23.519, "end": 23.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.619, "end": 23.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so-and-so", "start": 23.659, "end": 24.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.079, "end": 24.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 24.1, "end": 24.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.199, "end": 24.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so", "start": 24.219, "end": 24.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.359, "end": 24.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "years.\"", "start": 24.42, "end": 24.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.94, "end": 25.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 25.439, "end": 25.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.52, "end": 25.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "then", "start": 25.539, "end": 25.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.659, "end": 25.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 25.68, "end": 25.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.76, "end": 25.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "said", "start": 25.799, "end": 25.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.959, "end": 25.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 25.959, "end": 26.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.059, "end": 26.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "one", "start": 26.119, "end": 26.26, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.26, "end": 26.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "thing", "start": 26.319, "end": 26.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.459, "end": 26.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 26.479, "end": 26.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.599, "end": 26.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nobody", "start": 26.659, "end": 26.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.999, "end": 27.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "should", "start": 27.019, "end": 27.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.159, "end": 27.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ever", "start": 27.18, "end": 27.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.34, "end": 27.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "say", "start": 27.359, "end": 27.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.519, "end": 27.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 27.539, "end": 27.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.619, "end": 27.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me.", "start": 27.639, "end": 27.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.86, "end": 28.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 28.159, "end": 28.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.28, "end": 28.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "said,", "start": 28.34, "end": 28.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.579, "end": 28.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "\"Why", "start": 28.579, "end": 29.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.339, "end": 29.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "don't", "start": 29.34, "end": 29.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.519, "end": 29.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "you", "start": 29.539, "end": 29.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.639, "end": 29.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "do", "start": 29.719, "end": 29.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.819, "end": 29.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 29.84, "end": 29.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.939, "end": 29.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "yourself?\"", "start": 29.959, "end": 30.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.619, "end": 31.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 31.819, "end": 31.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.92, "end": 31.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "did.", "start": 31.979, "end": 32.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.339, "end": 43.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": ".", "start": 43.079, "end": 43.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.079, "end": 47.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "That's", "start": 47.84, "end": 48.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.04, "end": 48.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 48.059, "end": 48.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.36, "end": 48.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 48.379, "end": 48.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.899, "end": 49.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 49.379, "end": 49.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.439, "end": 49.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "young", "start": 49.459, "end": 49.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.619, "end": 49.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "director", "start": 49.68, "end": 50.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.02, "end": 50.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 50.059, "end": 50.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.159, "end": 50.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "got", "start": 50.219, "end": 50.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.399, "end": 50.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 50.419, "end": 50.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.52, "end": 50.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "take", "start": 50.559, "end": 50.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.72, "end": 50.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "over.", "start": 50.759, "end": 51.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.12, "end": 51.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 51.559, "end": 51.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.68, "end": 51.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "did", "start": 51.68, "end": 51.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.799, "end": 51.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 51.819, "end": 51.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.88, "end": 51.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "great", "start": 51.959, "end": 52.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.119, "end": 52.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "job.", "start": 52.18, "end": 52.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.659, "end": 57.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": ".", "start": 57.5, "end": 57.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.5, "end": 57.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "We", "start": 57.52, "end": 57.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.659, "end": 57.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "shot", "start": 57.719, "end": 57.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.959, "end": 58.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 58.0, "end": 58.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.119, "end": 58.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "every", "start": 58.159, "end": 58.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.379, "end": 58.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "country", "start": 58.419, "end": 58.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.68, "end": 58.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 58.719, "end": 58.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.879, "end": 58.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "we", "start": 58.879, "end": 59.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.0, "end": 59.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "show", "start": 59.059, "end": 59.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.279, "end": 59.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 59.279, "end": 59.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.36, "end": 59.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Around", "start": 59.399, "end": 59.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.599, "end": 59.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 59.659, "end": 59.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.719, "end": 59.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "World.", "start": 59.779, "end": 60.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.219, "end": 62.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "My", "start": 62.639, "end": 62.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.779, "end": 62.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "friend,", "start": 62.879, "end": 63.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.139, "end": 63.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 63.159, "end": 63.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.239, "end": 63.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "King", "start": 63.319, "end": 63.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.479, "end": 63.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 63.479, "end": 63.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.56, "end": 63.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Siam,", "start": 63.599, "end": 64.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.079, "end": 64.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "loaned", "start": 64.08, "end": 64.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.279, "end": 64.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me", "start": 64.299, "end": 64.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.4, "end": 64.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 64.419, "end": 64.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.5, "end": 64.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "royal", "start": 64.54, "end": 64.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.76, "end": 64.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "barge.", "start": 64.86, "end": 65.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.399, "end": 66.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 66.299, "end": 66.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.419, "end": 66.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "once", "start": 66.439, "end": 66.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.619, "end": 66.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wrote", "start": 66.619, "end": 66.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.819, "end": 66.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "some", "start": 66.819, "end": 66.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.959, "end": 67.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "songs", "start": 67.019, "end": 67.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.319, "end": 67.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 67.319, "end": 67.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.459, "end": 67.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "one", "start": 67.479, "end": 67.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.579, "end": 67.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 67.58, "end": 67.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.639, "end": 67.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "my", "start": 67.68, "end": 67.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.779, "end": 67.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cultural", "start": 67.879, "end": 68.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.219, "end": 68.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "achievements,", "start": 68.239, "end": 68.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.899, "end": 69.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Peep", "start": 69.659, "end": 69.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.9, "end": 69.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Show.", "start": 69.919, "end": 70.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.339, "end": 77.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "In", "start": 77.059, "end": 77.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 77.059, "end": 77.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Hollywood,", "start": 77.099, "end": 77.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 77.68, "end": 78.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "we", "start": 78.04, "end": 78.16, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.16, "end": 78.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "shot", "start": 78.199, "end": 78.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.399, "end": 78.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "this", "start": 78.419, "end": 78.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.559, "end": 78.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sequence", "start": 78.619, "end": 79.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.059, "end": 79.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "where", "start": 79.08, "end": 79.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.22, "end": 79.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Phileas", "start": 79.299, "end": 79.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.719, "end": 79.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 79.799, "end": 80.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.12, "end": 80.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "makes", "start": 80.139, "end": 80.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.359, "end": 80.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 80.379, "end": 80.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.459, "end": 80.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "last", "start": 80.54, "end": 80.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.8, "end": 80.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "leg", "start": 80.819, "end": 81.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.0, "end": 81.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 81.019, "end": 81.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.099, "end": 81.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 81.099, "end": 81.26, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.26, "end": 81.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trip", "start": 81.279, "end": 81.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.5, "end": 81.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "from", "start": 81.519, "end": 81.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.659, "end": 81.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "New", "start": 81.699, "end": 81.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.799, "end": 81.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "York", "start": 81.839, "end": 81.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.999, "end": 82.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 82.04, "end": 82.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.139, "end": 82.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Liverpool.", "start": 82.159, "end": 82.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.739, "end": 83.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 83.619, "end": 83.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.739, "end": 83.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dismantles", "start": 83.779, "end": 84.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.279, "end": 84.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 84.319, "end": 84.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.399, "end": 84.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ship", "start": 84.459, "end": 84.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.74, "end": 84.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 84.839, "end": 85.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.019, "end": 85.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "burns", "start": 85.059, "end": 85.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.299, "end": 85.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 85.299, "end": 85.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.379, "end": 85.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 85.4, "end": 85.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.54, "end": 85.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fuel.", "start": 85.559, "end": 85.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.979, "end": 93.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "My", "start": 93.119, "end": 93.26, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.26, "end": 93.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "best", "start": 93.299, "end": 93.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.5, "end": 93.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "friend,", "start": 93.54, "end": 93.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.759, "end": 93.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 93.779, "end": 93.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.839, "end": 93.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "good", "start": 93.839, "end": 93.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.979, "end": 94.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "director", "start": 94.019, "end": 94.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.479, "end": 95.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 95.04, "end": 95.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.219, "end": 95.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "watching,", "start": 95.279, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 96.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 96.459, "end": 96.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.559, "end": 96.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 96.559, "end": 96.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.659, "end": 96.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "said,", "start": 96.72, "end": 96.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.919, "end": 96.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "\"What", "start": 96.919, "end": 97.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.519, "end": 97.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 97.54, "end": 97.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.599, "end": 97.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "beautiful", "start": 97.699, "end": 98.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.08, "end": 98.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "miniature.", "start": 98.119, "end": 98.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.759, "end": 99.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Look", "start": 99.4, "end": 99.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.539, "end": 99.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "at", "start": 99.559, "end": 99.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.639, "end": 99.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 99.659, "end": 99.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.819, "end": 99.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "smoke,", "start": 99.879, "end": 100.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.239, "end": 100.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 100.239, "end": 100.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.319, "end": 100.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so-and-so", "start": 100.36, "end": 100.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.76, "end": 100.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 100.779, "end": 100.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.9, "end": 100.9, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so-and-so.", "start": 100.9, "end": 101.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.299, "end": 101.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 101.299, "end": 101.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.379, "end": 101.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "isn't", "start": 101.379, "end": 101.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.559, "end": 101.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 101.579, "end": 101.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.659, "end": 101.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "great?\"", "start": 101.68, "end": 102.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 102.159, "end": 103.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Well,", "start": 103.259, "end": 103.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 103.54, "end": 103.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 103.559, "end": 103.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 103.639, "end": 103.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 103.639, "end": 103.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 103.819, "end": 103.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "all", "start": 103.819, "end": 103.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 103.979, "end": 104.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "right,", "start": 104.059, "end": 104.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.319, "end": 104.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "but", "start": 104.339, "end": 104.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.479, "end": 105.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it's", "start": 105.0, "end": 105.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.159, "end": 105.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "late", "start": 105.18, "end": 105.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.359, "end": 105.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 105.399, "end": 105.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.459, "end": 105.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 105.459, "end": 105.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.559, "end": 105.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "picture", "start": 105.599, "end": 105.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.839, "end": 105.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 105.86, "end": 105.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.939, "end": 105.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "everything", "start": 105.959, "end": 106.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.239, "end": 106.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "we", "start": 106.239, "end": 106.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.379, "end": 106.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "do", "start": 106.399, "end": 106.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.519, "end": 106.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 106.519, "end": 106.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.58, "end": 106.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 106.599, "end": 106.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.659, "end": 106.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "picture", "start": 106.699, "end": 106.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.939, "end": 106.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "is", "start": 106.959, "end": 107.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 107.08, "end": 107.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "really", "start": 107.119, "end": 107.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 107.339, "end": 107.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "real.", "start": 107.419, "end": 107.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 107.76, "end": 109.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "When", "start": 109.139, "end": 109.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.259, "end": 109.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 109.259, "end": 109.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.339, "end": 109.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "say", "start": 109.379, "end": 109.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.519, "end": 109.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "late", "start": 109.54, "end": 109.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.739, "end": 109.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 109.739, "end": 109.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.799, "end": 109.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 109.799, "end": 109.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.859, "end": 109.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "picture,", "start": 109.919, "end": 110.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 110.239, "end": 110.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "just", "start": 110.259, "end": 110.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 110.439, "end": 110.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "about", "start": 110.459, "end": 110.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 110.699, "end": 110.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 110.699, "end": 110.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 110.839, "end": 110.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "last", "start": 110.879, "end": 111.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 111.22, "end": 111.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "real", "start": 111.259, "end": 111.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 111.519, "end": 111.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "big", "start": 111.619, "end": 111.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 111.799, "end": 111.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "production", "start": 111.839, "end": 112.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 112.299, "end": 112.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "number", "start": 112.339, "end": 112.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 112.54, "end": 112.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "before", "start": 112.559, "end": 112.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 112.839, "end": 112.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "we", "start": 112.879, "end": 112.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 112.979, "end": 113.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wind", "start": 113.019, "end": 113.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.239, "end": 113.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up", "start": 113.239, "end": 113.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.36, "end": 113.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 113.36, "end": 113.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.439, "end": 113.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "story.", "start": 113.459, "end": 113.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.919, "end": 114.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "This", "start": 114.759, "end": 114.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.919, "end": 114.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "whole", "start": 114.939, "end": 115.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 115.099, "end": 115.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sequence,", "start": 115.159, "end": 115.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 115.739, "end": 116.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "now", "start": 116.099, "end": 116.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 116.219, "end": 116.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 116.219, "end": 116.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 116.339, "end": 116.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "looked", "start": 116.339, "end": 116.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 116.54, "end": 116.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "at", "start": 116.559, "end": 116.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 116.699, "end": 116.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it,", "start": 116.719, "end": 116.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 116.899, "end": 117.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 117.379, "end": 117.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 117.459, "end": 117.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "didn't", "start": 117.459, "end": 117.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 117.639, "end": 117.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "like", "start": 117.659, "end": 117.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 117.819, "end": 117.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it,", "start": 117.839, "end": 118.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 118.019, "end": 118.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 118.659, "end": 118.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 118.759, "end": 118.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 118.779, "end": 118.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 118.879, "end": 118.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "worried", "start": 118.919, "end": 119.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 119.219, "end": 119.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me.", "start": 119.239, "end": 119.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 119.459, "end": 120.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "So", "start": 120.339, "end": 120.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 120.479, "end": 120.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 120.5, "end": 120.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 120.599, "end": 120.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "took", "start": 120.619, "end": 120.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 120.739, "end": 120.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 120.759, "end": 120.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 120.819, "end": 120.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "loss,", "start": 120.86, "end": 121.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 121.359, "end": 121.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 121.86, "end": 121.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 121.959, "end": 122.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 122.019, "end": 122.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 122.099, "end": 122.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "decided", "start": 122.119, "end": 122.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 122.519, "end": 122.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 122.519, "end": 122.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 122.599, "end": 122.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "buy", "start": 122.659, "end": 122.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 122.799, "end": 122.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 122.819, "end": 122.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 122.919, "end": 122.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "boat.", "start": 122.959, "end": 123.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.319, "end": 125.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 125.739, "end": 125.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 125.739, "end": 125.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "made", "start": 125.779, "end": 125.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 125.899, "end": 125.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 125.919, "end": 126.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.0, "end": 126.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "side-wheeler", "start": 126.019, "end": 126.62, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.62, "end": 126.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "out", "start": 126.639, "end": 126.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.759, "end": 126.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 126.759, "end": 126.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.819, "end": 126.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it,", "start": 126.86, "end": 127.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 127.1, "end": 131.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 131.139, "end": 131.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.279, "end": 131.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "took", "start": 131.339, "end": 131.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.519, "end": 131.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 131.539, "end": 131.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.639, "end": 131.66, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "out", "start": 131.66, "end": 131.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.819, "end": 131.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 131.819, "end": 131.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.879, "end": 131.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 131.879, "end": 131.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.979, "end": 132.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ocean", "start": 132.02, "end": 132.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.379, "end": 132.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 132.479, "end": 132.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.619, "end": 132.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dismantled", "start": 132.639, "end": 133.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.199, "end": 133.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it,", "start": 133.22, "end": 133.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.439, "end": 133.66, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 133.66, "end": 133.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.779, "end": 133.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it's", "start": 133.8, "end": 133.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.979, "end": 134.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 134.02, "end": 134.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.119, "end": 134.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "real.", "start": 134.179, "end": 134.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.559, "end": 140.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "We're", "start": 140.02, "end": 140.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 140.179, "end": 140.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "coming.", "start": 140.199, "end": 140.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 140.979, "end": 141.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Over.", "start": 141.259, "end": 141.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.9, "end": 142.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Barge", "start": 142.039, "end": 144.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.259, "end": 144.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "to", "start": 144.279, "end": 144.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.399, "end": 144.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "the", "start": 144.419, "end": 144.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.499, "end": 144.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Henrietta,", "start": 144.539, "end": 145.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 145.319, "end": 145.8, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "we're", "start": 145.8, "end": 146.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 146.019, "end": 146.02, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ready.", "start": 146.02, "end": 146.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 146.419, "end": 146.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Come", "start": 146.679, "end": 146.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 146.839, "end": 146.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "right", "start": 146.879, "end": 147.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 147.039, "end": 147.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "to", "start": 147.059, "end": 147.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 147.339, "end": 147.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "us.", "start": 147.339, "end": 147.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 147.559, "end": 147.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Come", "start": 147.779, "end": 147.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 147.939, "end": 148.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "full", "start": 148.0, "end": 148.18, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.18, "end": 148.3, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "steam", "start": 148.3, "end": 148.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.559, "end": 148.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ahead.", "start": 148.559, "end": 149.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.019, "end": 149.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "We're", "start": 149.139, "end": 149.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.339, "end": 149.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "coming", "start": 149.36, "end": 149.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.679, "end": 149.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 149.679, "end": 149.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.8, "end": 149.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "on", "start": 149.879, "end": 149.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.899, "end": 150.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "everything", "start": 150.0, "end": 150.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.159, "end": 150.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "you", "start": 150.259, "end": 150.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.339, "end": 150.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "say.", "start": 150.399, "end": 150.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.739, "end": 150.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Over.", "start": 150.759, "end": 151.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 151.339, "end": 151.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "I", "start": 151.459, "end": 151.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 151.479, "end": 152.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "gave", "start": 152.099, "end": 152.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.279, "end": 152.3, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "you", "start": 152.3, "end": 152.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.379, "end": 152.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "the", "start": 152.379, "end": 152.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.459, "end": 152.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "word", "start": 152.5, "end": 152.74, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.74, "end": 152.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "right", "start": 152.759, "end": 153.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 153.019, "end": 153.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "now,", "start": 153.039, "end": 153.3, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 153.3, "end": 153.3, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "\"Come", "start": 153.3, "end": 153.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 153.479, "end": 153.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "on", "start": 153.519, "end": 153.66, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 153.66, "end": 153.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ahead.\"", "start": 153.679, "end": 154.08, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.08, "end": 154.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Is", "start": 154.199, "end": 154.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.279, "end": 154.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "that", "start": 154.279, "end": 154.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.44, "end": 154.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "clear?", "start": 154.459, "end": 154.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.879, "end": 155.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Very", "start": 155.5, "end": 155.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.799, "end": 155.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "clear.", "start": 155.86, "end": 156.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.199, "end": 156.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Thank", "start": 156.259, "end": 156.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.479, "end": 156.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "you.", "start": 156.479, "end": 156.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.779, "end": 158.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Very", "start": 158.259, "end": 158.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 158.559, "end": 158.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "good,", "start": 158.66, "end": 158.96, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 158.96, "end": 159.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Barge", "start": 159.019, "end": 159.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.359, "end": 159.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "to", "start": 159.419, "end": 159.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.559, "end": 159.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "the", "start": 159.599, "end": 159.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.659, "end": 159.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Henrietta.", "start": 159.679, "end": 160.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.219, "end": 160.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "That's", "start": 160.239, "end": 160.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.459, "end": 160.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "it.", "start": 160.459, "end": 160.68, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.68, "end": 160.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Now", "start": 160.72, "end": 160.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.86, "end": 160.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "keep", "start": 160.94, "end": 161.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.159, "end": 161.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "going.", "start": 161.199, "end": 161.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.739, "end": 162.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Keep", "start": 162.619, "end": 162.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.839, "end": 162.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "going", "start": 162.899, "end": 163.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.339, "end": 163.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "and", "start": 163.879, "end": 164.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 164.0, "end": 164.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "make,", "start": 164.019, "end": 164.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 164.339, "end": 164.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "uh,", "start": 164.339, "end": 164.54, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 164.54, "end": 164.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "and,", "start": 164.94, "end": 165.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.139, "end": 165.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "and", "start": 165.139, "end": 165.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.259, "end": 165.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "get", "start": 165.319, "end": 165.52, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.52, "end": 165.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "out,", "start": 165.539, "end": 165.82, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.82, "end": 165.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "get", "start": 165.879, "end": 166.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.039, "end": 166.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "around", "start": 166.039, "end": 166.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.339, "end": 166.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 166.36, "end": 166.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.479, "end": 166.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "front", "start": 166.539, "end": 166.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.72, "end": 166.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "of", "start": 166.739, "end": 166.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.819, "end": 166.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "us.", "start": 166.839, "end": 167.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.079, "end": 167.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "When", "start": 167.519, "end": 167.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.659, "end": 167.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "you", "start": 167.66, "end": 167.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.72, "end": 167.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "get", "start": 167.72, "end": 167.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.839, "end": 167.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 167.86, "end": 167.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.959, "end": 167.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "front", "start": 167.979, "end": 168.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 168.159, "end": 168.16, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "of", "start": 168.16, "end": 168.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 168.239, "end": 168.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "the", "start": 168.259, "end": 168.36, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 168.36, "end": 168.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cameras,", "start": 168.399, "end": 168.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 168.919, "end": 169.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "swing", "start": 169.22, "end": 169.46, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.46, "end": 169.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "all", "start": 169.479, "end": 169.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.659, "end": 169.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "the", "start": 169.66, "end": 169.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.739, "end": 169.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "way", "start": 169.779, "end": 169.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.919, "end": 169.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "around.", "start": 169.94, "end": 170.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.379, "end": 170.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "This", "start": 170.399, "end": 170.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.58, "end": 170.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "is", "start": 170.619, "end": 170.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.759, "end": 170.8, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "the,", "start": 170.8, "end": 171.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 171.019, "end": 171.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "uh,", "start": 171.019, "end": 171.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 171.099, "end": 171.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sharp", "start": 171.179, "end": 171.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 171.459, "end": 171.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "turn.", "start": 171.519, "end": 172.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.059, "end": 172.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "We", "start": 172.66, "end": 172.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.759, "end": 172.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "want", "start": 172.779, "end": 172.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.919, "end": 172.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 172.959, "end": 172.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.979, "end": 173.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lot", "start": 173.019, "end": 173.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 173.179, "end": 173.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "of", "start": 173.179, "end": 173.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 173.259, "end": 173.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "activity", "start": 173.319, "end": 174.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.059, "end": 174.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "along.", "start": 174.119, "end": 174.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.799, "end": 175.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Okay.", "start": 175.22, "end": 175.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.779, "end": 176.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Over", "start": 176.3, "end": 176.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.559, "end": 176.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 176.599, "end": 176.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.699, "end": 176.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "out.", "start": 176.699, "end": 177.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 177.259, "end": 193.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "We", "start": 193.739, "end": 193.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.96, "end": 193.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "started", "start": 193.979, "end": 194.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 194.359, "end": 194.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 194.379, "end": 194.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 194.479, "end": 194.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "August", "start": 194.519, "end": 194.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 194.819, "end": 195.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 195.699, "end": 195.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.86, "end": 195.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "finished", "start": 195.919, "end": 196.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.219, "end": 196.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 196.22, "end": 196.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.319, "end": 196.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "end", "start": 196.36, "end": 196.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.479, "end": 196.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 196.5, "end": 196.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.579, "end": 196.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "December.", "start": 196.619, "end": 197.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 197.18, "end": 202.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Then", "start": 202.44, "end": 202.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 202.72, "end": 202.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "came", "start": 202.759, "end": 202.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 202.96, "end": 203.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "little", "start": 203.019, "end": 203.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 203.199, "end": 203.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "things", "start": 203.3, "end": 203.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 203.619, "end": 203.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "like", "start": 203.679, "end": 203.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 203.859, "end": 203.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "scoring", "start": 203.94, "end": 204.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 204.52, "end": 205.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 205.459, "end": 205.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 205.58, "end": 205.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cutting", "start": 205.679, "end": 206.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 206.039, "end": 206.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 206.519, "end": 206.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 206.639, "end": 206.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "piecing", "start": 206.679, "end": 207.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 207.0, "end": 207.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "together.", "start": 207.019, "end": 207.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 207.559, "end": 209.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "But", "start": 209.08, "end": 209.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.22, "end": 209.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 209.339, "end": 209.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.36, "end": 209.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "looked", "start": 209.379, "end": 209.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.559, "end": 209.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "at", "start": 209.58, "end": 209.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.66, "end": 209.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 209.679, "end": 209.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.739, "end": 209.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "prologue", "start": 209.839, "end": 210.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 210.319, "end": 210.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 210.339, "end": 210.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 210.419, "end": 210.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 210.44, "end": 210.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 210.54, "end": 210.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 210.559, "end": 210.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 210.72, "end": 210.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "phony.", "start": 210.799, "end": 211.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 211.379, "end": 213.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 213.86, "end": 214.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.039, "end": 214.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "threw", "start": 214.039, "end": 214.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.219, "end": 214.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 214.239, "end": 214.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.299, "end": 214.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "away", "start": 214.319, "end": 214.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.699, "end": 214.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 214.979, "end": 215.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.139, "end": 215.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spent", "start": 215.159, "end": 215.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.399, "end": 215.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "an", "start": 215.419, "end": 215.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.52, "end": 215.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "extra", "start": 215.539, "end": 215.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.779, "end": 215.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "half", "start": 215.819, "end": 216.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.0, "end": 216.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "million", "start": 216.099, "end": 216.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.339, "end": 216.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "on", "start": 216.339, "end": 216.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.459, "end": 216.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 216.5, "end": 216.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.58, "end": 216.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "whole", "start": 216.599, "end": 216.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.8, "end": 216.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "new", "start": 216.86, "end": 217.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.059, "end": 217.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "prologue", "start": 217.059, "end": 217.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.5, "end": 217.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "just", "start": 217.539, "end": 217.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.699, "end": 217.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "before", "start": 217.72, "end": 217.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.979, "end": 217.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "we", "start": 217.979, "end": 218.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.119, "end": 218.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "opened", "start": 218.139, "end": 218.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.36, "end": 218.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 218.36, "end": 218.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.479, "end": 218.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "picture.", "start": 218.5, "end": 218.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 10.443871974945068, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "eng", "language_probability": 0.9884020090103149, "text": "At 11:00, I saw it was completely hopeless. And I saw that mistake. And so I asked this guy, I said, \"How does so-and-so and so get to so-and-so?\" And he took 12 or 20 or 30 minutes, and he explained it to me. This guy says, \"I've been making pictures for so-and-so and so-and-so and so years.\" And then he said the one thing that nobody should ever say to me. He said, \"Why don't you do it yourself?\" I did. . That's <PERSON>, the young director I got to take over. He did a great job. . We shot in every country that we show in Around the World. My friend, the King of Siam, loaned me the royal barge. He once wrote some songs for one of my cultural achievements, Peep Show. In Hollywood, we shot this sequence where <PERSON><PERSON> makes the last leg of his trip from New York to Liverpool. He dismantles the ship and burns it for fuel. My best friend, a good director was watching, and he said, \"What a beautiful miniature. Look at that smoke, and so-and-so and so-and-so. And isn't it great?\" Well, it was all right, but it's late in the picture and everything we do in the picture is really real. When I say late in the picture, just about the last real big production number before we wind up the story. This whole sequence, now I looked at it, I didn't like it, and it worried me. So I took a loss, and I decided to buy a boat. I made a side-wheeler out of it, and took it out in the ocean and dismantled it, and it's for real. We're coming. Over. Barge to the Henrietta, we're ready. Come right to us. Come full steam ahead. We're coming in on everything you say. Over. I gave you the word right now, \"Come on ahead.\" Is that clear? Very clear. Thank you. Very good, Barge to the Henrietta. That's it. Now keep going. Keep going and make, uh, and, and get out, get around in front of us. When you get in front of the cameras, swing all the way around. This is the, uh, sharp turn. We want a lot of activity along. Okay. Over and out. We started in August and finished the end of December. Then came little things like scoring and cutting and piecing together. But I looked at the prologue and it was phony. I threw it away and spent an extra half million on a whole new prologue just before we opened the picture.", "words": [{"text": "At", "start": 1.979, "end": 2.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.099, "end": 2.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "11:00,", "start": 2.159, "end": 2.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.739, "end": 3.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 3.759, "end": 4.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.039, "end": 4.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "saw", "start": 4.079, "end": 4.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.359, "end": 4.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 4.42, "end": 4.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.559, "end": 4.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 4.579, "end": 4.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.739, "end": 4.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "completely", "start": 4.819, "end": 5.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.539, "end": 5.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hopeless.", "start": 5.619, "end": 6.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.279, "end": 7.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 7.319, "end": 7.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.439, "end": 7.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 7.519, "end": 7.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.579, "end": 7.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "saw", "start": 7.579, "end": 7.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.759, "end": 7.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 7.779, "end": 7.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.899, "end": 7.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "mistake.", "start": 7.919, "end": 8.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.44, "end": 9.46, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 9.46, "end": 9.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.559, "end": 9.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so", "start": 9.599, "end": 9.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.719, "end": 9.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 9.739, "end": 9.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.84, "end": 9.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "asked", "start": 9.859, "end": 10.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.079, "end": 10.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "this", "start": 10.099, "end": 10.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.279, "end": 10.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "guy,", "start": 10.3, "end": 10.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.679, "end": 11.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 11.259, "end": 11.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.379, "end": 11.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "said,", "start": 11.399, "end": 11.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.559, "end": 11.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "\"How", "start": 11.559, "end": 11.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.759, "end": 11.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "does", "start": 11.779, "end": 11.98, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.98, "end": 12.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so-and-so", "start": 12.0, "end": 12.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.519, "end": 12.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 12.559, "end": 12.64, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.64, "end": 12.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so", "start": 12.719, "end": 12.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.92, "end": 12.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "get", "start": 12.96, "end": 13.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.139, "end": 13.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 13.139, "end": 13.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.279, "end": 13.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so-and-so?\"", "start": 13.319, "end": 14.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.12, "end": 14.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 14.799, "end": 14.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.94, "end": 14.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 14.96, "end": 15.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.079, "end": 15.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "took", "start": 15.079, "end": 15.26, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.26, "end": 15.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "12", "start": 15.339, "end": 15.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.859, "end": 15.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "or", "start": 15.899, "end": 16.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.0, "end": 16.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "20", "start": 16.119, "end": 16.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.459, "end": 16.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "or", "start": 16.459, "end": 16.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.579, "end": 16.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "30", "start": 16.699, "end": 16.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.94, "end": 16.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "minutes,", "start": 16.959, "end": 17.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.299, "end": 17.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 17.6, "end": 17.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.699, "end": 17.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 17.699, "end": 17.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.799, "end": 17.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "explained", "start": 17.84, "end": 18.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.259, "end": 18.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 18.26, "end": 18.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.359, "end": 18.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 18.379, "end": 18.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.44, "end": 18.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me.", "start": 18.5, "end": 18.7, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.7, "end": 20.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "This", "start": 20.439, "end": 20.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.619, "end": 20.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "guy", "start": 20.639, "end": 20.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.819, "end": 20.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "says,", "start": 20.899, "end": 21.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.219, "end": 21.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "\"I've", "start": 21.219, "end": 22.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.159, "end": 22.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "been", "start": 22.18, "end": 22.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.299, "end": 22.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "making", "start": 22.299, "end": 22.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.559, "end": 22.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pictures", "start": 22.619, "end": 22.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.899, "end": 22.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 22.92, "end": 23.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.039, "end": 23.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so-and-so", "start": 23.059, "end": 23.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.5, "end": 23.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 23.519, "end": 23.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.619, "end": 23.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so-and-so", "start": 23.659, "end": 24.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.079, "end": 24.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 24.1, "end": 24.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.199, "end": 24.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so", "start": 24.219, "end": 24.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.359, "end": 24.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "years.\"", "start": 24.42, "end": 24.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.94, "end": 25.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 25.439, "end": 25.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.52, "end": 25.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "then", "start": 25.539, "end": 25.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.659, "end": 25.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 25.68, "end": 25.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.76, "end": 25.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "said", "start": 25.799, "end": 25.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.959, "end": 25.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 25.959, "end": 26.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.059, "end": 26.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "one", "start": 26.119, "end": 26.26, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.26, "end": 26.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "thing", "start": 26.319, "end": 26.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.459, "end": 26.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 26.479, "end": 26.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.599, "end": 26.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "nobody", "start": 26.659, "end": 26.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.999, "end": 27.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "should", "start": 27.019, "end": 27.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.159, "end": 27.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ever", "start": 27.18, "end": 27.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.34, "end": 27.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "say", "start": 27.359, "end": 27.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.519, "end": 27.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 27.539, "end": 27.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.619, "end": 27.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me.", "start": 27.639, "end": 27.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.86, "end": 28.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 28.159, "end": 28.28, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.28, "end": 28.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "said,", "start": 28.34, "end": 28.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.579, "end": 28.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "\"Why", "start": 28.579, "end": 29.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.339, "end": 29.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "don't", "start": 29.34, "end": 29.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.519, "end": 29.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "you", "start": 29.539, "end": 29.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.639, "end": 29.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "do", "start": 29.719, "end": 29.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.819, "end": 29.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 29.84, "end": 29.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.939, "end": 29.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "yourself?\"", "start": 29.959, "end": 30.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.619, "end": 31.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 31.819, "end": 31.92, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.92, "end": 31.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "did.", "start": 31.979, "end": 32.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.339, "end": 43.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": ".", "start": 43.079, "end": 43.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.079, "end": 47.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "That's", "start": 47.84, "end": 48.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.04, "end": 48.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 48.059, "end": 48.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.36, "end": 48.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 48.379, "end": 48.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.899, "end": 49.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 49.379, "end": 49.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.439, "end": 49.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "young", "start": 49.459, "end": 49.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.619, "end": 49.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "director", "start": 49.68, "end": 50.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.02, "end": 50.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 50.059, "end": 50.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.159, "end": 50.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "got", "start": 50.219, "end": 50.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.399, "end": 50.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 50.419, "end": 50.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.52, "end": 50.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "take", "start": 50.559, "end": 50.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.72, "end": 50.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "over.", "start": 50.759, "end": 51.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.12, "end": 51.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 51.559, "end": 51.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.68, "end": 51.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "did", "start": 51.68, "end": 51.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.799, "end": 51.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 51.819, "end": 51.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.88, "end": 51.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "great", "start": 51.959, "end": 52.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.119, "end": 52.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "job.", "start": 52.18, "end": 52.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.659, "end": 57.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": ".", "start": 57.5, "end": 57.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.5, "end": 57.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "We", "start": 57.52, "end": 57.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.659, "end": 57.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "shot", "start": 57.719, "end": 57.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.959, "end": 58.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 58.0, "end": 58.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.119, "end": 58.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "every", "start": 58.159, "end": 58.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.379, "end": 58.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "country", "start": 58.419, "end": 58.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.68, "end": 58.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 58.719, "end": 58.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.879, "end": 58.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "we", "start": 58.879, "end": 59.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.0, "end": 59.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "show", "start": 59.059, "end": 59.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.279, "end": 59.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 59.279, "end": 59.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.36, "end": 59.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Around", "start": 59.399, "end": 59.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.599, "end": 59.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 59.659, "end": 59.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.719, "end": 59.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "World.", "start": 59.779, "end": 60.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.219, "end": 62.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "My", "start": 62.639, "end": 62.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.779, "end": 62.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "friend,", "start": 62.879, "end": 63.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.139, "end": 63.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 63.159, "end": 63.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.239, "end": 63.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "King", "start": 63.319, "end": 63.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.479, "end": 63.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 63.479, "end": 63.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.56, "end": 63.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Siam,", "start": 63.599, "end": 64.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.079, "end": 64.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "loaned", "start": 64.08, "end": 64.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.279, "end": 64.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me", "start": 64.299, "end": 64.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.4, "end": 64.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 64.419, "end": 64.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.5, "end": 64.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "royal", "start": 64.54, "end": 64.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.76, "end": 64.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "barge.", "start": 64.86, "end": 65.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.399, "end": 66.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 66.299, "end": 66.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.419, "end": 66.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "once", "start": 66.439, "end": 66.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.619, "end": 66.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wrote", "start": 66.619, "end": 66.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.819, "end": 66.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "some", "start": 66.819, "end": 66.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.959, "end": 67.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "songs", "start": 67.019, "end": 67.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.319, "end": 67.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 67.319, "end": 67.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.459, "end": 67.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "one", "start": 67.479, "end": 67.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.579, "end": 67.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 67.58, "end": 67.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.639, "end": 67.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "my", "start": 67.68, "end": 67.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.779, "end": 67.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cultural", "start": 67.879, "end": 68.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.219, "end": 68.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "achievements,", "start": 68.239, "end": 68.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.899, "end": 69.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Peep", "start": 69.659, "end": 69.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.9, "end": 69.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Show.", "start": 69.919, "end": 70.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.339, "end": 77.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "In", "start": 77.059, "end": 77.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 77.059, "end": 77.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Hollywood,", "start": 77.099, "end": 77.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 77.68, "end": 78.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "we", "start": 78.04, "end": 78.16, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.16, "end": 78.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "shot", "start": 78.199, "end": 78.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.399, "end": 78.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "this", "start": 78.419, "end": 78.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 78.559, "end": 78.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sequence", "start": 78.619, "end": 79.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.059, "end": 79.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "where", "start": 79.08, "end": 79.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.22, "end": 79.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Phileas", "start": 79.299, "end": 79.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 79.719, "end": 79.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 79.799, "end": 80.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.12, "end": 80.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "makes", "start": 80.139, "end": 80.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.359, "end": 80.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 80.379, "end": 80.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.459, "end": 80.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "last", "start": 80.54, "end": 80.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 80.8, "end": 80.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "leg", "start": 80.819, "end": 81.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.0, "end": 81.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 81.019, "end": 81.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.099, "end": 81.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 81.099, "end": 81.26, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.26, "end": 81.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "trip", "start": 81.279, "end": 81.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.5, "end": 81.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "from", "start": 81.519, "end": 81.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.659, "end": 81.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "New", "start": 81.699, "end": 81.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.799, "end": 81.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "York", "start": 81.839, "end": 81.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 81.999, "end": 82.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 82.04, "end": 82.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.139, "end": 82.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Liverpool.", "start": 82.159, "end": 82.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 82.739, "end": 83.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 83.619, "end": 83.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 83.739, "end": 83.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dismantles", "start": 83.779, "end": 84.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.279, "end": 84.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 84.319, "end": 84.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.399, "end": 84.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ship", "start": 84.459, "end": 84.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 84.74, "end": 84.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 84.839, "end": 85.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.019, "end": 85.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "burns", "start": 85.059, "end": 85.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.299, "end": 85.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 85.299, "end": 85.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.379, "end": 85.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 85.4, "end": 85.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.54, "end": 85.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fuel.", "start": 85.559, "end": 85.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 85.979, "end": 93.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "My", "start": 93.119, "end": 93.26, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.26, "end": 93.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "best", "start": 93.299, "end": 93.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.5, "end": 93.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "friend,", "start": 93.54, "end": 93.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.759, "end": 93.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 93.779, "end": 93.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.839, "end": 93.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "good", "start": 93.839, "end": 93.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 93.979, "end": 94.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "director", "start": 94.019, "end": 94.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 94.479, "end": 95.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 95.04, "end": 95.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.219, "end": 95.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "watching,", "start": 95.279, "end": 95.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 95.759, "end": 96.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 96.459, "end": 96.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.559, "end": 96.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 96.559, "end": 96.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.659, "end": 96.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "said,", "start": 96.72, "end": 96.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 96.919, "end": 96.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "\"What", "start": 96.919, "end": 97.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.519, "end": 97.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 97.54, "end": 97.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 97.599, "end": 97.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "beautiful", "start": 97.699, "end": 98.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.08, "end": 98.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "miniature.", "start": 98.119, "end": 98.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 98.759, "end": 99.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Look", "start": 99.4, "end": 99.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.539, "end": 99.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "at", "start": 99.559, "end": 99.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.639, "end": 99.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 99.659, "end": 99.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 99.819, "end": 99.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "smoke,", "start": 99.879, "end": 100.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.239, "end": 100.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 100.239, "end": 100.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.319, "end": 100.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so-and-so", "start": 100.36, "end": 100.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.76, "end": 100.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 100.779, "end": 100.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 100.9, "end": 100.9, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so-and-so.", "start": 100.9, "end": 101.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.299, "end": 101.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 101.299, "end": 101.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.379, "end": 101.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "isn't", "start": 101.379, "end": 101.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.559, "end": 101.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 101.579, "end": 101.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 101.659, "end": 101.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "great?\"", "start": 101.68, "end": 102.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 102.159, "end": 103.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Well,", "start": 103.259, "end": 103.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 103.54, "end": 103.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 103.559, "end": 103.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 103.639, "end": 103.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 103.639, "end": 103.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 103.819, "end": 103.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "all", "start": 103.819, "end": 103.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 103.979, "end": 104.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "right,", "start": 104.059, "end": 104.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.319, "end": 104.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "but", "start": 104.339, "end": 104.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 104.479, "end": 105.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it's", "start": 105.0, "end": 105.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.159, "end": 105.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "late", "start": 105.18, "end": 105.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.359, "end": 105.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 105.399, "end": 105.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.459, "end": 105.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 105.459, "end": 105.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.559, "end": 105.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "picture", "start": 105.599, "end": 105.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.839, "end": 105.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 105.86, "end": 105.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 105.939, "end": 105.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "everything", "start": 105.959, "end": 106.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.239, "end": 106.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "we", "start": 106.239, "end": 106.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.379, "end": 106.399, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "do", "start": 106.399, "end": 106.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.519, "end": 106.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 106.519, "end": 106.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.58, "end": 106.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 106.599, "end": 106.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.659, "end": 106.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "picture", "start": 106.699, "end": 106.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 106.939, "end": 106.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "is", "start": 106.959, "end": 107.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 107.08, "end": 107.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "really", "start": 107.119, "end": 107.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 107.339, "end": 107.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "real.", "start": 107.419, "end": 107.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 107.76, "end": 109.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "When", "start": 109.139, "end": 109.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.259, "end": 109.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 109.259, "end": 109.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.339, "end": 109.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "say", "start": 109.379, "end": 109.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.519, "end": 109.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "late", "start": 109.54, "end": 109.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.739, "end": 109.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 109.739, "end": 109.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.799, "end": 109.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 109.799, "end": 109.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 109.859, "end": 109.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "picture,", "start": 109.919, "end": 110.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 110.239, "end": 110.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "just", "start": 110.259, "end": 110.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 110.439, "end": 110.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "about", "start": 110.459, "end": 110.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 110.699, "end": 110.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 110.699, "end": 110.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 110.839, "end": 110.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "last", "start": 110.879, "end": 111.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 111.22, "end": 111.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "real", "start": 111.259, "end": 111.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 111.519, "end": 111.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "big", "start": 111.619, "end": 111.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 111.799, "end": 111.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "production", "start": 111.839, "end": 112.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 112.299, "end": 112.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "number", "start": 112.339, "end": 112.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 112.54, "end": 112.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "before", "start": 112.559, "end": 112.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 112.839, "end": 112.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "we", "start": 112.879, "end": 112.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 112.979, "end": 113.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wind", "start": 113.019, "end": 113.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.239, "end": 113.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up", "start": 113.239, "end": 113.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.36, "end": 113.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 113.36, "end": 113.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.439, "end": 113.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "story.", "start": 113.459, "end": 113.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 113.919, "end": 114.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "This", "start": 114.759, "end": 114.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 114.919, "end": 114.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "whole", "start": 114.939, "end": 115.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 115.099, "end": 115.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sequence,", "start": 115.159, "end": 115.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 115.739, "end": 116.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "now", "start": 116.099, "end": 116.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 116.219, "end": 116.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 116.219, "end": 116.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 116.339, "end": 116.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "looked", "start": 116.339, "end": 116.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 116.54, "end": 116.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "at", "start": 116.559, "end": 116.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 116.699, "end": 116.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it,", "start": 116.719, "end": 116.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 116.899, "end": 117.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 117.379, "end": 117.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 117.459, "end": 117.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "didn't", "start": 117.459, "end": 117.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 117.639, "end": 117.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "like", "start": 117.659, "end": 117.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 117.819, "end": 117.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it,", "start": 117.839, "end": 118.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 118.019, "end": 118.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 118.659, "end": 118.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 118.759, "end": 118.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 118.779, "end": 118.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 118.879, "end": 118.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "worried", "start": 118.919, "end": 119.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 119.219, "end": 119.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me.", "start": 119.239, "end": 119.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 119.459, "end": 120.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "So", "start": 120.339, "end": 120.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 120.479, "end": 120.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 120.5, "end": 120.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 120.599, "end": 120.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "took", "start": 120.619, "end": 120.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 120.739, "end": 120.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 120.759, "end": 120.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 120.819, "end": 120.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "loss,", "start": 120.86, "end": 121.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 121.359, "end": 121.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 121.86, "end": 121.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 121.959, "end": 122.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 122.019, "end": 122.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 122.099, "end": 122.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "decided", "start": 122.119, "end": 122.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 122.519, "end": 122.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 122.519, "end": 122.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 122.599, "end": 122.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "buy", "start": 122.659, "end": 122.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 122.799, "end": 122.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 122.819, "end": 122.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 122.919, "end": 122.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "boat.", "start": 122.959, "end": 123.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 123.319, "end": 125.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 125.739, "end": 125.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 125.739, "end": 125.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "made", "start": 125.779, "end": 125.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 125.899, "end": 125.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 125.919, "end": 126.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.0, "end": 126.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "side-wheeler", "start": 126.019, "end": 126.62, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.62, "end": 126.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "out", "start": 126.639, "end": 126.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.759, "end": 126.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 126.759, "end": 126.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 126.819, "end": 126.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it,", "start": 126.86, "end": 127.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 127.1, "end": 131.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 131.139, "end": 131.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.279, "end": 131.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "took", "start": 131.339, "end": 131.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.519, "end": 131.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 131.539, "end": 131.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.639, "end": 131.66, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "out", "start": 131.66, "end": 131.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.819, "end": 131.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 131.819, "end": 131.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.879, "end": 131.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 131.879, "end": 131.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 131.979, "end": 132.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ocean", "start": 132.02, "end": 132.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.379, "end": 132.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 132.479, "end": 132.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 132.619, "end": 132.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dismantled", "start": 132.639, "end": 133.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.199, "end": 133.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it,", "start": 133.22, "end": 133.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.439, "end": 133.66, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 133.66, "end": 133.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.779, "end": 133.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it's", "start": 133.8, "end": 133.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 133.979, "end": 134.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 134.02, "end": 134.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.119, "end": 134.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "real.", "start": 134.179, "end": 134.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 134.559, "end": 140.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "We're", "start": 140.02, "end": 140.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 140.179, "end": 140.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "coming.", "start": 140.199, "end": 140.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 140.979, "end": 141.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Over.", "start": 141.259, "end": 141.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.9, "end": 142.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Barge", "start": 142.039, "end": 144.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.259, "end": 144.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "to", "start": 144.279, "end": 144.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.399, "end": 144.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "the", "start": 144.419, "end": 144.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 144.499, "end": 144.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Henrietta,", "start": 144.539, "end": 145.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 145.319, "end": 145.8, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "we're", "start": 145.8, "end": 146.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 146.019, "end": 146.02, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ready.", "start": 146.02, "end": 146.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 146.419, "end": 146.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Come", "start": 146.679, "end": 146.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 146.839, "end": 146.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "right", "start": 146.879, "end": 147.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 147.039, "end": 147.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "to", "start": 147.059, "end": 147.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 147.339, "end": 147.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "us.", "start": 147.339, "end": 147.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 147.559, "end": 147.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Come", "start": 147.779, "end": 147.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 147.939, "end": 148.0, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "full", "start": 148.0, "end": 148.18, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.18, "end": 148.3, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "steam", "start": 148.3, "end": 148.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 148.559, "end": 148.559, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ahead.", "start": 148.559, "end": 149.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 149.019, "end": 149.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "We're", "start": 149.139, "end": 149.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.339, "end": 149.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "coming", "start": 149.36, "end": 149.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.679, "end": 149.679, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 149.679, "end": 149.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.8, "end": 149.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "on", "start": 149.879, "end": 149.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 149.899, "end": 150.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "everything", "start": 150.0, "end": 150.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.159, "end": 150.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "you", "start": 150.259, "end": 150.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.339, "end": 150.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "say.", "start": 150.399, "end": 150.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 150.739, "end": 150.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Over.", "start": 150.759, "end": 151.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 151.339, "end": 151.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "I", "start": 151.459, "end": 151.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 151.479, "end": 152.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "gave", "start": 152.099, "end": 152.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.279, "end": 152.3, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "you", "start": 152.3, "end": 152.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.379, "end": 152.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "the", "start": 152.379, "end": 152.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.459, "end": 152.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "word", "start": 152.5, "end": 152.74, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 152.74, "end": 152.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "right", "start": 152.759, "end": 153.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 153.019, "end": 153.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "now,", "start": 153.039, "end": 153.3, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 153.3, "end": 153.3, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "\"Come", "start": 153.3, "end": 153.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 153.479, "end": 153.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "on", "start": 153.519, "end": 153.66, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 153.66, "end": 153.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ahead.\"", "start": 153.679, "end": 154.08, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.08, "end": 154.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Is", "start": 154.199, "end": 154.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.279, "end": 154.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "that", "start": 154.279, "end": 154.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.44, "end": 154.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "clear?", "start": 154.459, "end": 154.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 154.879, "end": 155.5, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Very", "start": 155.5, "end": 155.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 155.799, "end": 155.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "clear.", "start": 155.86, "end": 156.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.199, "end": 156.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Thank", "start": 156.259, "end": 156.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.479, "end": 156.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "you.", "start": 156.479, "end": 156.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 156.779, "end": 158.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Very", "start": 158.259, "end": 158.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 158.559, "end": 158.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "good,", "start": 158.66, "end": 158.96, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 158.96, "end": 159.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Barge", "start": 159.019, "end": 159.359, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.359, "end": 159.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "to", "start": 159.419, "end": 159.559, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.559, "end": 159.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "the", "start": 159.599, "end": 159.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 159.659, "end": 159.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Henrietta.", "start": 159.679, "end": 160.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.219, "end": 160.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "That's", "start": 160.239, "end": 160.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.459, "end": 160.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "it.", "start": 160.459, "end": 160.68, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.68, "end": 160.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Now", "start": 160.72, "end": 160.86, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.86, "end": 160.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "keep", "start": 160.94, "end": 161.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.159, "end": 161.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "going.", "start": 161.199, "end": 161.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 161.739, "end": 162.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Keep", "start": 162.619, "end": 162.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 162.839, "end": 162.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "going", "start": 162.899, "end": 163.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.339, "end": 163.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "and", "start": 163.879, "end": 164.0, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 164.0, "end": 164.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "make,", "start": 164.019, "end": 164.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 164.339, "end": 164.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "uh,", "start": 164.339, "end": 164.54, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 164.54, "end": 164.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "and,", "start": 164.94, "end": 165.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.139, "end": 165.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "and", "start": 165.139, "end": 165.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.259, "end": 165.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "get", "start": 165.319, "end": 165.52, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.52, "end": 165.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "out,", "start": 165.539, "end": 165.82, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 165.82, "end": 165.879, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "get", "start": 165.879, "end": 166.039, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.039, "end": 166.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "around", "start": 166.039, "end": 166.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.339, "end": 166.36, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 166.36, "end": 166.479, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.479, "end": 166.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "front", "start": 166.539, "end": 166.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.72, "end": 166.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "of", "start": 166.739, "end": 166.819, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 166.819, "end": 166.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "us.", "start": 166.839, "end": 167.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.079, "end": 167.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "When", "start": 167.519, "end": 167.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.659, "end": 167.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "you", "start": 167.66, "end": 167.72, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.72, "end": 167.72, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "get", "start": 167.72, "end": 167.839, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.839, "end": 167.86, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "in", "start": 167.86, "end": 167.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 167.959, "end": 167.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "front", "start": 167.979, "end": 168.159, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 168.159, "end": 168.16, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "of", "start": 168.16, "end": 168.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 168.239, "end": 168.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "the", "start": 168.259, "end": 168.36, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 168.36, "end": 168.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "cameras,", "start": 168.399, "end": 168.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 168.919, "end": 169.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "swing", "start": 169.22, "end": 169.46, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.46, "end": 169.479, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "all", "start": 169.479, "end": 169.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.659, "end": 169.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "the", "start": 169.66, "end": 169.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.739, "end": 169.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "way", "start": 169.779, "end": 169.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 169.919, "end": 169.94, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "around.", "start": 169.94, "end": 170.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.379, "end": 170.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "This", "start": 170.399, "end": 170.58, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.58, "end": 170.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "is", "start": 170.619, "end": 170.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 170.759, "end": 170.8, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "the,", "start": 170.8, "end": 171.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 171.019, "end": 171.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "uh,", "start": 171.019, "end": 171.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 171.099, "end": 171.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "sharp", "start": 171.179, "end": 171.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 171.459, "end": 171.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "turn.", "start": 171.519, "end": 172.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.059, "end": 172.66, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "We", "start": 172.66, "end": 172.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.759, "end": 172.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "want", "start": 172.779, "end": 172.919, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.919, "end": 172.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "a", "start": 172.959, "end": 172.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 172.979, "end": 173.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "lot", "start": 173.019, "end": 173.179, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 173.179, "end": 173.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "of", "start": 173.179, "end": 173.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 173.259, "end": 173.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "activity", "start": 173.319, "end": 174.059, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.059, "end": 174.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "along.", "start": 174.119, "end": 174.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 174.799, "end": 175.22, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Okay.", "start": 175.22, "end": 175.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 175.779, "end": 176.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Over", "start": 176.3, "end": 176.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.559, "end": 176.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 176.599, "end": 176.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 176.699, "end": 176.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "out.", "start": 176.699, "end": 177.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 177.259, "end": 193.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "We", "start": 193.739, "end": 193.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 193.96, "end": 193.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "started", "start": 193.979, "end": 194.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 194.359, "end": 194.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 194.379, "end": 194.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 194.479, "end": 194.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "August", "start": 194.519, "end": 194.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 194.819, "end": 195.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 195.699, "end": 195.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 195.86, "end": 195.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "finished", "start": 195.919, "end": 196.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.219, "end": 196.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 196.22, "end": 196.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.319, "end": 196.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "end", "start": 196.36, "end": 196.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.479, "end": 196.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 196.5, "end": 196.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 196.579, "end": 196.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "December.", "start": 196.619, "end": 197.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 197.18, "end": 202.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Then", "start": 202.44, "end": 202.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 202.72, "end": 202.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "came", "start": 202.759, "end": 202.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 202.96, "end": 203.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "little", "start": 203.019, "end": 203.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 203.199, "end": 203.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "things", "start": 203.3, "end": 203.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 203.619, "end": 203.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "like", "start": 203.679, "end": 203.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 203.859, "end": 203.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "scoring", "start": 203.94, "end": 204.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 204.52, "end": 205.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 205.459, "end": 205.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 205.58, "end": 205.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cutting", "start": 205.679, "end": 206.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 206.039, "end": 206.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 206.519, "end": 206.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 206.639, "end": 206.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "piecing", "start": 206.679, "end": 207.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 207.0, "end": 207.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "together.", "start": 207.019, "end": 207.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 207.559, "end": 209.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "But", "start": 209.08, "end": 209.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.22, "end": 209.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 209.339, "end": 209.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.36, "end": 209.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "looked", "start": 209.379, "end": 209.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.559, "end": 209.58, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "at", "start": 209.58, "end": 209.66, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.66, "end": 209.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 209.679, "end": 209.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 209.739, "end": 209.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "prologue", "start": 209.839, "end": 210.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 210.319, "end": 210.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 210.339, "end": 210.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 210.419, "end": 210.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 210.44, "end": 210.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 210.54, "end": 210.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 210.559, "end": 210.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 210.72, "end": 210.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "phony.", "start": 210.799, "end": 211.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 211.379, "end": 213.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 213.86, "end": 214.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.039, "end": 214.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "threw", "start": 214.039, "end": 214.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.219, "end": 214.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it", "start": 214.239, "end": 214.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.299, "end": 214.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "away", "start": 214.319, "end": 214.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 214.699, "end": 214.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 214.979, "end": 215.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.139, "end": 215.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spent", "start": 215.159, "end": 215.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.399, "end": 215.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "an", "start": 215.419, "end": 215.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.52, "end": 215.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "extra", "start": 215.539, "end": 215.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 215.779, "end": 215.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "half", "start": 215.819, "end": 216.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.0, "end": 216.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "million", "start": 216.099, "end": 216.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.339, "end": 216.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "on", "start": 216.339, "end": 216.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.459, "end": 216.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 216.5, "end": 216.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.58, "end": 216.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "whole", "start": 216.599, "end": 216.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 216.8, "end": 216.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "new", "start": 216.86, "end": 217.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.059, "end": 217.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "prologue", "start": 217.059, "end": 217.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.5, "end": 217.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "just", "start": 217.539, "end": 217.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.699, "end": 217.72, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "before", "start": 217.72, "end": 217.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 217.979, "end": 217.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "we", "start": 217.979, "end": 218.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.119, "end": 218.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "opened", "start": 218.139, "end": 218.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.36, "end": 218.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 218.36, "end": 218.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 218.479, "end": 218.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "picture.", "start": 218.5, "end": 218.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}]}}, "created_at": 1754281600.1593285}