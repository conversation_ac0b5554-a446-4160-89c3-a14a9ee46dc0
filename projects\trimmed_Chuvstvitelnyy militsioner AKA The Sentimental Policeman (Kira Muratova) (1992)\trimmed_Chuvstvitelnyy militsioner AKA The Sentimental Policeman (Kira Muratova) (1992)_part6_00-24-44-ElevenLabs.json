{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754307440", "text": "Лучше всех знала, как добраться до школы. Но теперь не знала, как оттуда уйти. Я бежала к школе по тропинке, которая проходила через поле. Поле было очень высокое, и мне приходилось подниматься на высокий порог. Когда я бежала из школы, я думала об одном только one way. Я хотела дойти до дома, как можно быстрее. Я бежала к школе по тропинке, которая проходила через поле. Поле было очень высокое, и мне приходилось подниматься на высокий порог. Когда я бежала из школы, я думала об одном только one way. Я хотела дойти до дома, как можно быстрее. Я бежала к школе по тропинке, которая проходила через поле. Поле было очень высокое, и мне приходилось подниматься на высокий порог. Когда я бежала из школы, я думала об одном только one way. Я хотела дойти до дома, как можно быстрее. Я бежала к школе по тропинке, которая проходила через поле. Поле было очень высокое, и мне приходилось подниматься на высокий порог. Когда я бежала из школы, я думала об одном только one way. Я хотела дойти до дома, как можно быстрее. ", "words": [{"text": "<PERSON>у<PERSON><PERSON>е", "start": 0.099, "end": 1.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.459, "end": 1.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "всех", "start": 1.459, "end": 1.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.559, "end": 1.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "знала,", "start": 1.559, "end": 1.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.839, "end": 2.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "как", "start": 2.5, "end": 2.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.519, "end": 2.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "добраться", "start": 2.519, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "до", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "школы.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Но", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "теперь", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "не", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "знала,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "как", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "оттуда", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "уйти.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "к", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "школе", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "по", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "тропинке,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "которая", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "проходила", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "через", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "поле.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Поле", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "было", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "очень", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокое,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "и", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "мне", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "приходилось", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "подниматься", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "на", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокий", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "порог.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Когда", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "из", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "школы,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "думала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "об", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "одном", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "только", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "one", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "way.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "хотела", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дойти", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "до", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дома,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "как", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "можно", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "быстрее.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "к", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "школе", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "по", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "тропинке,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "которая", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "проходила", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "через", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "поле.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Поле", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "было", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "очень", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокое,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "и", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "мне", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "приходилось", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "подниматься", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "на", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокий", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "порог.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Когда", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "из", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "школы,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "думала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "об", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "одном", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "только", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "one", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "way.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "хотела", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дойти", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "до", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дома,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "как", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "можно", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "быстрее.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "к", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "школе", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "по", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "тропинке,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "которая", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "проходила", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "через", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "поле.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Поле", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "было", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "очень", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокое,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "и", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "мне", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "приходилось", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "подниматься", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "на", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокий", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "порог.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Когда", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "из", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "школы,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "думала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "об", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "одном", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "только", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "one", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "way.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "хотела", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дойти", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "до", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дома,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "как", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "можно", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "быстрее.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "к", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "школе", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "по", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "тропинке,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "которая", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "проходила", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "через", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "поле.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Поле", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "было", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "очень", "start": 2.799, "end": 24.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.199, "end": 24.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокое,", "start": 24.199, "end": 24.3, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.3, "end": 24.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "и", "start": 24.3, "end": 24.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.299, "end": 24.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "мне", "start": 24.299, "end": 24.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.299, "end": 24.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "приходилось", "start": 24.299, "end": 26.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.94, "end": 26.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "подниматься", "start": 26.94, "end": 27.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.36, "end": 27.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "на", "start": 27.36, "end": 27.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.659, "end": 27.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокий", "start": 27.659, "end": 29.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.399, "end": 30.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "порог.", "start": 30.26, "end": 43.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.84, "end": 43.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Когда", "start": 43.84, "end": 44.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.4, "end": 44.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 44.459, "end": 44.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.479, "end": 44.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "бежала", "start": 44.479, "end": 188.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.959, "end": 192.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "из", "start": 192.559, "end": 196.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.54, "end": 227.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "школы,", "start": 227.899, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "думала", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "об", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "одном", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "только", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "one", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "way.", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "хотела", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дойти", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "до", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дома,", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "как", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "можно", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "быстрее.", "start": 353.299, "end": 355.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 355.799, "end": 358.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 22.003929138183594, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "rus", "language_probability": 1.0, "text": "Лучше всех знала, как добраться до школы. Но теперь не знала, как оттуда уйти. Я бежала к школе по тропинке, которая проходила через поле. Поле было очень высокое, и мне приходилось подниматься на высокий порог. Когда я бежала из школы, я думала об одном только one way. Я хотела дойти до дома, как можно быстрее. Я бежала к школе по тропинке, которая проходила через поле. Поле было очень высокое, и мне приходилось подниматься на высокий порог. Когда я бежала из школы, я думала об одном только one way. Я хотела дойти до дома, как можно быстрее. Я бежала к школе по тропинке, которая проходила через поле. Поле было очень высокое, и мне приходилось подниматься на высокий порог. Когда я бежала из школы, я думала об одном только one way. Я хотела дойти до дома, как можно быстрее. Я бежала к школе по тропинке, которая проходила через поле. Поле было очень высокое, и мне приходилось подниматься на высокий порог. Когда я бежала из школы, я думала об одном только one way. Я хотела дойти до дома, как можно быстрее. ", "words": [{"text": "<PERSON>у<PERSON><PERSON>е", "start": 0.099, "end": 1.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.459, "end": 1.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "всех", "start": 1.459, "end": 1.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.559, "end": 1.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "знала,", "start": 1.559, "end": 1.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.839, "end": 2.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "как", "start": 2.5, "end": 2.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.519, "end": 2.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "добраться", "start": 2.519, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "до", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "школы.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Но", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "теперь", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "не", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "знала,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "как", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "оттуда", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "уйти.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "к", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "школе", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "по", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "тропинке,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "которая", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "проходила", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "через", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "поле.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Поле", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "было", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "очень", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокое,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "и", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "мне", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "приходилось", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "подниматься", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "на", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокий", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "порог.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Когда", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "из", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "школы,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "думала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "об", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "одном", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "только", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "one", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "way.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "хотела", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дойти", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "до", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дома,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "как", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "можно", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "быстрее.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "к", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "школе", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "по", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "тропинке,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "которая", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "проходила", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "через", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "поле.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Поле", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "было", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "очень", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокое,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "и", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "мне", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "приходилось", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "подниматься", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "на", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокий", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "порог.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Когда", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "из", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "школы,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "думала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "об", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "одном", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "только", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "one", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "way.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "хотела", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дойти", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "до", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дома,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "как", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "можно", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "быстрее.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "к", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "школе", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "по", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "тропинке,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "которая", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "проходила", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "через", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "поле.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Поле", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "было", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "очень", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокое,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "и", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "мне", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "приходилось", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "подниматься", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "на", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокий", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "порог.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Когда", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "из", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "школы,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "думала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "об", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "одном", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "только", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "one", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "way.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "хотела", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дойти", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "до", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дома,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "как", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "можно", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "быстрее.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "бежала", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "к", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "школе", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "по", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "тропинке,", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "которая", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "проходила", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "через", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "поле.", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Поле", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "было", "start": 2.799, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 2.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "очень", "start": 2.799, "end": 24.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.199, "end": 24.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокое,", "start": 24.199, "end": 24.3, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.3, "end": 24.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "и", "start": 24.3, "end": 24.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.299, "end": 24.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "мне", "start": 24.299, "end": 24.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.299, "end": 24.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "приходилось", "start": 24.299, "end": 26.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.94, "end": 26.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "подниматься", "start": 26.94, "end": 27.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.36, "end": 27.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "на", "start": 27.36, "end": 27.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.659, "end": 27.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "высокий", "start": 27.659, "end": 29.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.399, "end": 30.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "порог.", "start": 30.26, "end": 43.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.84, "end": 43.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Когда", "start": 43.84, "end": 44.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.4, "end": 44.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 44.459, "end": 44.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.479, "end": 44.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "бежала", "start": 44.479, "end": 188.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 188.959, "end": 192.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "из", "start": 192.559, "end": 196.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 196.54, "end": 227.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "школы,", "start": 227.899, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "я", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "думала", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "об", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "одном", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "только", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "one", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "way.", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Я", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "хотела", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дойти", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "до", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "дома,", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "как", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "можно", "start": 353.299, "end": 353.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 353.299, "end": 353.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "быстрее.", "start": 353.299, "end": 355.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 355.799, "end": 358.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}]}}, "created_at": 1754307462.9299674}