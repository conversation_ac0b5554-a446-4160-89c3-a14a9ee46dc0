{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754281525", "text": "... came to our neighborhood in Chicago called the Wicker Park area and <PERSON> was a pretty sharp little kid, even at those days. He decided to run the crap games in the schoolyard of the school called Wicker Park. Now, he had to knock, uh, two fellas, or fight two fellas, uh, and matter of fact, they beat him to death but they admired his, uh, his intestinal fortitude so they let him run the games and then he became a partner with these guys. And I was to get 10 cents, I'll be honest with you ladies and gentlemen, to be the lookout. For those that know the gangster jargon, I was to watch out for the cops. Incidentally, I never was paid and if he were alive today, I believe that he would owe me about $140,000 in interest. <PERSON>, as <PERSON><PERSON><PERSON><PERSON> was nicknamed, left school when he was 12 having become the youngest apprentice pharmacist in the history of the State of Illinois. During prohibition, he made a small fortune selling medicinal alcohol. He took a wife when he was barely 17 and still in his teens, he set up a promotional outfit that specialized in high pressure close out sales. When the talkies came in, he moved to California and built sound stages. As a contractor, <PERSON> made and lost a million twice by the time he was 21. Having heard the words spoken on the sound stages, he became convinced he could write. This was when the crash of '29 left him without a construction business. So at the height of the Depression, he returned to Chicago and scratched a living writing jokes and sketches for radio and vaudeville. But it was the Chicago World's Fair of '32 where he made his first big score in show business with an act elegantly labeled, \"The Moth and the Flame.\" This involved a lady wearing a transparent asbestos garment under the one you see. The gimmick, of course, was that she flitted too close to the flame and her costume got burned off. <PERSON>, in later years, said... I burned up four girls before I got it right. Actually, not even a false eyelash was singed. <PERSON> got hot and made money touring the flame dance and other attractions on the road. Now with a new press agent, <PERSON> Dahl, he was ready to attack Broadway. Many, many years ago, uh, I got a phone call from Mr. Todd.", "words": [{"text": "...", "start": 0.079, "end": 0.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 0.319, "end": 1.62, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "came", "start": 1.62, "end": 1.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.779, "end": 1.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 1.799, "end": 1.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.919, "end": 1.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "our", "start": 1.919, "end": 2.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.039, "end": 2.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "neighborhood", "start": 2.079, "end": 2.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.339, "end": 2.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 2.339, "end": 2.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.419, "end": 2.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Chicago", "start": 2.44, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 3.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "called", "start": 3.039, "end": 3.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.219, "end": 3.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 3.22, "end": 3.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.279, "end": 3.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Wicker", "start": 3.299, "end": 3.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.559, "end": 3.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Park", "start": 3.619, "end": 3.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.9, "end": 3.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "area", "start": 3.959, "end": 4.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.279, "end": 5.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 5.019, "end": 5.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.199, "end": 5.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 5.259, "end": 5.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.5, "end": 5.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 5.5, "end": 5.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.639, "end": 5.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 5.679, "end": 5.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.719, "end": 5.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pretty", "start": 5.779, "end": 6.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.019, "end": 6.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sharp", "start": 6.019, "end": 6.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.279, "end": 6.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "little", "start": 6.279, "end": 6.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.46, "end": 6.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "kid,", "start": 6.5, "end": 6.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.679, "end": 6.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "even", "start": 6.679, "end": 6.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.839, "end": 6.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "at", "start": 6.839, "end": 6.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.94, "end": 6.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "those", "start": 6.96, "end": 7.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.219, "end": 7.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "days.", "start": 7.219, "end": 7.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.659, "end": 8.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 8.559, "end": 8.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.719, "end": 8.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "decided", "start": 8.76, "end": 9.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.279, "end": 9.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 9.92, "end": 10.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.0, "end": 10.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "run", "start": 10.019, "end": 10.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.139, "end": 10.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 10.159, "end": 10.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.239, "end": 10.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "crap", "start": 10.3, "end": 10.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.539, "end": 10.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "games", "start": 10.559, "end": 10.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.859, "end": 10.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 10.859, "end": 10.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.939, "end": 10.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 10.96, "end": 11.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.06, "end": 11.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "schoolyard", "start": 11.219, "end": 11.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.779, "end": 12.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 12.719, "end": 12.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.899, "end": 12.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 12.899, "end": 12.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.979, "end": 12.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "school", "start": 12.979, "end": 13.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.239, "end": 13.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "called", "start": 13.259, "end": 13.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.439, "end": 13.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Wicker", "start": 13.439, "end": 13.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.699, "end": 13.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Park.", "start": 13.739, "end": 14.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.159, "end": 14.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Now,", "start": 14.719, "end": 14.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.859, "end": 14.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 14.88, "end": 14.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.96, "end": 14.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "had", "start": 14.96, "end": 15.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.079, "end": 15.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 15.079, "end": 15.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.139, "end": 15.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "knock,", "start": 15.139, "end": 15.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.56, "end": 15.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "uh,", "start": 15.699, "end": 15.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.72, "end": 16.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "two", "start": 16.299, "end": 16.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.44, "end": 16.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fellas,", "start": 16.539, "end": 16.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.959, "end": 17.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "or", "start": 17.54, "end": 17.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.659, "end": 17.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fight", "start": 17.76, "end": 17.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.959, "end": 18.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "two", "start": 18.02, "end": 18.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.159, "end": 18.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fellas,", "start": 18.219, "end": 18.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.619, "end": 18.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "uh,", "start": 18.639, "end": 18.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.899, "end": 19.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 19.76, "end": 19.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.88, "end": 19.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "matter", "start": 19.959, "end": 20.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.099, "end": 20.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 20.1, "end": 20.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.159, "end": 20.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fact,", "start": 20.199, "end": 20.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.34, "end": 20.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "they", "start": 20.359, "end": 20.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.5, "end": 20.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "beat", "start": 20.5, "end": 20.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.659, "end": 20.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "him", "start": 20.659, "end": 20.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.779, "end": 20.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 20.779, "end": 20.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.899, "end": 20.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "death", "start": 20.939, "end": 21.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.18, "end": 21.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "but", "start": 21.26, "end": 21.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.42, "end": 21.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "they", "start": 21.439, "end": 21.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.539, "end": 21.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "admired", "start": 21.539, "end": 21.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.959, "end": 21.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his,", "start": 21.979, "end": 22.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.279, "end": 22.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "uh,", "start": 22.279, "end": 22.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.439, "end": 22.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 22.439, "end": 22.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.579, "end": 22.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "intestinal", "start": 22.6, "end": 23.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.04, "end": 23.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fortitude", "start": 23.119, "end": 23.62, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.62, "end": 23.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so", "start": 23.699, "end": 23.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.88, "end": 23.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "they", "start": 23.959, "end": 24.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.079, "end": 24.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "let", "start": 24.079, "end": 24.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.199, "end": 24.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "him", "start": 24.199, "end": 24.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.299, "end": 24.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "run", "start": 24.299, "end": 24.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.439, "end": 24.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 24.439, "end": 24.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.54, "end": 24.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "games", "start": 24.559, "end": 24.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.799, "end": 24.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 24.799, "end": 24.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.899, "end": 24.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "then", "start": 24.92, "end": 25.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.039, "end": 25.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 25.039, "end": 25.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.159, "end": 25.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "became", "start": 25.159, "end": 25.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.42, "end": 25.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 25.459, "end": 25.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.559, "end": 25.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "partner", "start": 25.559, "end": 25.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.859, "end": 25.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "with", "start": 25.859, "end": 26.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.02, "end": 26.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "these", "start": 26.039, "end": 26.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.239, "end": 26.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "guys.", "start": 26.299, "end": 26.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.679, "end": 27.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 27.219, "end": 27.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.319, "end": 27.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 27.34, "end": 27.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.42, "end": 27.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 27.42, "end": 27.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.559, "end": 27.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 27.579, "end": 27.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.659, "end": 27.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "get", "start": 27.659, "end": 27.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.84, "end": 27.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "10", "start": 27.84, "end": 28.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.02, "end": 28.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cents,", "start": 28.079, "end": 28.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.319, "end": 28.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I'll", "start": 28.34, "end": 28.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.459, "end": 28.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "be", "start": 28.459, "end": 28.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.579, "end": 28.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "honest", "start": 28.579, "end": 28.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.799, "end": 28.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "with", "start": 28.819, "end": 28.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.919, "end": 28.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "you", "start": 28.92, "end": 28.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.979, "end": 29.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ladies", "start": 29.0, "end": 29.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.18, "end": 29.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 29.18, "end": 29.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.2, "end": 29.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gentlemen,", "start": 29.2, "end": 29.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.42, "end": 29.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 29.439, "end": 29.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.639, "end": 29.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "be", "start": 29.699, "end": 29.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.84, "end": 29.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 29.879, "end": 29.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.96, "end": 30.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lookout.", "start": 30.0, "end": 30.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.579, "end": 31.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "For", "start": 31.34, "end": 31.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.46, "end": 31.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "those", "start": 31.479, "end": 31.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.679, "end": 31.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 31.679, "end": 31.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.78, "end": 31.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "know", "start": 31.799, "end": 31.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.959, "end": 32.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 32.0, "end": 32.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.139, "end": 32.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gangster", "start": 32.599, "end": 33.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.119, "end": 33.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "jargon,", "start": 33.2, "end": 33.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.619, "end": 33.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 33.659, "end": 33.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.84, "end": 34.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 34.159, "end": 34.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.279, "end": 34.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 34.299, "end": 34.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.38, "end": 34.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "watch", "start": 34.419, "end": 34.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.639, "end": 34.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "out", "start": 34.659, "end": 34.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.819, "end": 34.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 34.919, "end": 35.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.04, "end": 35.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 35.04, "end": 35.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.119, "end": 35.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cops.", "start": 35.2, "end": 35.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.54, "end": 35.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Incidentally,", "start": 35.54, "end": 35.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.959, "end": 35.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 35.959, "end": 36.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.0, "end": 36.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "never", "start": 36.04, "end": 36.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.18, "end": 36.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 36.2, "end": 36.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.38, "end": 36.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paid", "start": 36.439, "end": 36.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.739, "end": 36.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 36.779, "end": 36.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.88, "end": 36.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "if", "start": 36.899, "end": 37.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.0, "end": 37.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 37.0, "end": 37.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.119, "end": 37.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "were", "start": 37.119, "end": 37.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.219, "end": 37.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "alive", "start": 37.219, "end": 37.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.459, "end": 37.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "today,", "start": 37.479, "end": 37.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.859, "end": 38.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 38.239, "end": 38.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.339, "end": 38.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "believe", "start": 38.34, "end": 38.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.559, "end": 38.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 38.559, "end": 38.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.68, "end": 38.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 38.68, "end": 38.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.779, "end": 38.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "would", "start": 38.779, "end": 38.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.899, "end": 38.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "owe", "start": 38.899, "end": 39.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.02, "end": 39.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me", "start": 39.04, "end": 39.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.119, "end": 39.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "about", "start": 39.159, "end": 39.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.299, "end": 39.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "$140,000", "start": 39.299, "end": 40.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.479, "end": 40.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 40.5, "end": 40.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.56, "end": 40.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "interest.", "start": 40.579, "end": 41.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.059, "end": 43.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 43.86, "end": 44.46, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.46, "end": 44.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "as", "start": 44.639, "end": 44.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.799, "end": 44.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 44.84, "end": 45.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.199, "end": 45.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 45.2, "end": 45.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.34, "end": 45.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nicknamed,", "start": 45.379, "end": 45.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.94, "end": 46.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "left", "start": 46.419, "end": 46.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.599, "end": 46.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "school", "start": 46.639, "end": 46.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.94, "end": 46.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "when", "start": 46.959, "end": 47.06, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.06, "end": 47.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 47.079, "end": 47.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.139, "end": 47.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 47.159, "end": 47.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.359, "end": 47.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "12", "start": 47.379, "end": 47.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.759, "end": 47.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "having", "start": 47.799, "end": 48.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.04, "end": 48.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "become", "start": 48.059, "end": 48.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.319, "end": 48.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 48.36, "end": 48.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.439, "end": 48.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "youngest", "start": 48.5, "end": 48.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.86, "end": 48.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "apprentice", "start": 48.879, "end": 49.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.459, "end": 49.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pharmacist", "start": 49.539, "end": 50.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.119, "end": 50.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 50.139, "end": 50.24, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.24, "end": 50.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 50.259, "end": 50.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.34, "end": 50.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "history", "start": 50.399, "end": 50.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.759, "end": 50.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 50.779, "end": 50.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.84, "end": 50.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 50.86, "end": 50.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.919, "end": 50.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "State", "start": 50.979, "end": 51.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.199, "end": 51.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 51.199, "end": 51.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.279, "end": 51.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Illinois.", "start": 51.319, "end": 51.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.879, "end": 52.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "During", "start": 52.439, "end": 52.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.659, "end": 52.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prohibition,", "start": 52.719, "end": 53.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.419, "end": 53.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 53.899, "end": 54.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.0, "end": 54.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "made", "start": 54.02, "end": 54.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.139, "end": 54.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 54.159, "end": 54.2, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.2, "end": 54.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "small", "start": 54.239, "end": 54.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.5, "end": 54.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fortune", "start": 54.579, "end": 54.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.979, "end": 55.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "selling", "start": 55.02, "end": 55.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.319, "end": 55.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "medicinal", "start": 55.34, "end": 55.88, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.88, "end": 55.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alcohol.", "start": 55.919, "end": 56.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.579, "end": 57.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "He", "start": 57.219, "end": 57.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.359, "end": 57.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "took", "start": 57.36, "end": 57.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.479, "end": 57.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 57.5, "end": 57.56, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.56, "end": 57.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "wife", "start": 57.599, "end": 57.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.819, "end": 57.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "when", "start": 57.819, "end": 57.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.919, "end": 57.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 57.919, "end": 57.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.979, "end": 58.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 58.0, "end": 58.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.119, "end": 58.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "barely", "start": 58.119, "end": 58.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.4, "end": 58.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "17", "start": 58.439, "end": 59.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.059, "end": 59.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 59.159, "end": 59.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.299, "end": 59.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "still", "start": 59.34, "end": 59.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.559, "end": 59.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 59.559, "end": 59.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.639, "end": 59.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "his", "start": 59.639, "end": 59.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.799, "end": 59.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "teens,", "start": 59.84, "end": 60.28, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.28, "end": 60.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 60.559, "end": 60.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.68, "end": 60.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "set", "start": 60.699, "end": 60.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.84, "end": 60.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "up", "start": 60.859, "end": 60.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.959, "end": 60.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 60.979, "end": 61.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.079, "end": 61.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "promotional", "start": 61.079, "end": 61.56, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.56, "end": 61.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "outfit", "start": 61.599, "end": 61.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.92, "end": 61.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "that", "start": 61.939, "end": 62.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.079, "end": 62.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "specialized", "start": 62.079, "end": 62.7, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.7, "end": 62.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 62.739, "end": 62.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.819, "end": 62.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "high", "start": 62.879, "end": 63.06, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.06, "end": 63.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pressure", "start": 63.18, "end": 63.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.52, "end": 63.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "close", "start": 63.639, "end": 63.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.939, "end": 63.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "out", "start": 63.979, "end": 64.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.18, "end": 64.26, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sales.", "start": 64.26, "end": 64.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.879, "end": 65.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "When", "start": 65.639, "end": 65.76, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 65.76, "end": 65.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 65.779, "end": 65.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 65.839, "end": 65.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "talkies", "start": 65.879, "end": 66.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.299, "end": 66.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "came", "start": 66.299, "end": 66.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.5, "end": 66.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in,", "start": 66.54, "end": 66.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.759, "end": 67.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 67.04, "end": 67.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.159, "end": 67.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "moved", "start": 67.18, "end": 67.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.339, "end": 67.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 67.339, "end": 67.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.439, "end": 67.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "California", "start": 67.459, "end": 68.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.04, "end": 68.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 68.059, "end": 68.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.179, "end": 68.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "built", "start": 68.18, "end": 68.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.399, "end": 68.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sound", "start": 68.419, "end": 68.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.699, "end": 68.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stages.", "start": 68.739, "end": 69.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 69.44, "end": 69.9, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "As", "start": 69.9, "end": 70.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 70.019, "end": 70.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 70.04, "end": 70.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 70.139, "end": 70.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "contractor,", "start": 70.18, "end": 70.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 70.919, "end": 71.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 71.159, "end": 71.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.459, "end": 71.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "made", "start": 71.519, "end": 71.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.919, "end": 71.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 71.919, "end": 72.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 72.08, "end": 72.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lost", "start": 72.159, "end": 72.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 72.739, "end": 73.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 73.019, "end": 73.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.119, "end": 73.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "million", "start": 73.199, "end": 73.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.599, "end": 73.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "twice", "start": 73.839, "end": 74.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.279, "end": 74.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "by", "start": 74.339, "end": 74.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.44, "end": 74.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 74.459, "end": 74.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.559, "end": 74.58, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "time", "start": 74.58, "end": 74.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.739, "end": 74.76, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 74.76, "end": 74.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.839, "end": 74.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 74.86, "end": 74.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.979, "end": 75.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "21.", "start": 75.04, "end": 75.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.62, "end": 76.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Having", "start": 76.699, "end": 76.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.919, "end": 76.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "heard", "start": 76.939, "end": 77.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.139, "end": 77.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 77.159, "end": 77.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.239, "end": 77.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "words", "start": 77.299, "end": 77.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.619, "end": 77.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "spoken", "start": 77.659, "end": 78.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.059, "end": 78.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "on", "start": 78.059, "end": 78.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.159, "end": 78.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 78.18, "end": 78.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.239, "end": 78.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sound", "start": 78.299, "end": 78.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.54, "end": 78.58, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stages,", "start": 78.58, "end": 79.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.019, "end": 79.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 79.04, "end": 79.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.119, "end": 79.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "became", "start": 79.139, "end": 79.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.419, "end": 79.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "convinced", "start": 79.479, "end": 79.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.919, "end": 79.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 79.939, "end": 80.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.04, "end": 80.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "could", "start": 80.059, "end": 80.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.22, "end": 80.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "write.", "start": 80.239, "end": 80.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.68, "end": 81.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "This", "start": 81.5, "end": 81.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.639, "end": 81.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 81.659, "end": 81.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.779, "end": 81.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "when", "start": 81.779, "end": 81.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.899, "end": 81.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 81.919, "end": 81.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.979, "end": 82.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "crash", "start": 82.059, "end": 82.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.319, "end": 82.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 82.339, "end": 82.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.439, "end": 82.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "'29", "start": 82.439, "end": 82.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.979, "end": 83.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "left", "start": 83.019, "end": 83.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.239, "end": 83.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "him", "start": 83.239, "end": 83.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.359, "end": 83.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "without", "start": 83.36, "end": 83.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.599, "end": 83.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 83.639, "end": 83.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.699, "end": 83.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "construction", "start": 83.72, "end": 84.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.239, "end": 84.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "business.", "start": 84.299, "end": 84.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.8, "end": 85.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "So", "start": 85.54, "end": 85.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.68, "end": 85.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "at", "start": 85.68, "end": 85.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.779, "end": 85.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 85.799, "end": 85.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.879, "end": 85.9, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "height", "start": 85.9, "end": 86.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.119, "end": 86.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 86.119, "end": 86.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.199, "end": 86.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 86.199, "end": 86.26, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.26, "end": 86.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Depression,", "start": 86.299, "end": 86.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.899, "end": 87.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 87.18, "end": 87.26, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.26, "end": 87.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "returned", "start": 87.279, "end": 87.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.599, "end": 87.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 87.619, "end": 87.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.699, "end": 87.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Chicago", "start": 87.739, "end": 88.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.16, "end": 88.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 88.18, "end": 88.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.279, "end": 88.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "scratched", "start": 88.299, "end": 88.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.72, "end": 88.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 88.759, "end": 88.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.839, "end": 88.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "living", "start": 88.839, "end": 89.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.139, "end": 89.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "writing", "start": 89.159, "end": 89.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.5, "end": 89.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "jokes", "start": 89.559, "end": 89.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.899, "end": 89.9, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 89.9, "end": 89.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.999, "end": 90.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sketches", "start": 90.04, "end": 90.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 90.579, "end": 90.9, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "for", "start": 90.9, "end": 91.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.039, "end": 91.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "radio", "start": 91.059, "end": 91.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.379, "end": 91.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 91.379, "end": 91.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.459, "end": 91.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vaudeville.", "start": 91.519, "end": 92.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 92.059, "end": 93.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "But", "start": 93.119, "end": 93.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.199, "end": 93.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "it", "start": 93.199, "end": 93.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.259, "end": 93.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 93.259, "end": 93.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.379, "end": 93.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 93.379, "end": 93.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.479, "end": 93.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Chicago", "start": 93.519, "end": 93.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.94, "end": 94.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "World's", "start": 94.04, "end": 94.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 94.379, "end": 94.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Fair", "start": 94.459, "end": 94.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 94.739, "end": 94.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 94.739, "end": 94.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 94.839, "end": 94.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "'32", "start": 94.839, "end": 95.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 95.499, "end": 96.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "where", "start": 96.059, "end": 96.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.199, "end": 96.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 96.199, "end": 96.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.279, "end": 96.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "made", "start": 96.279, "end": 96.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.399, "end": 96.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "his", "start": 96.419, "end": 96.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.559, "end": 96.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "first", "start": 96.599, "end": 96.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.799, "end": 96.9, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "big", "start": 96.9, "end": 97.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.059, "end": 97.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "score", "start": 97.119, "end": 97.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.399, "end": 97.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 97.419, "end": 97.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.499, "end": 97.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "show", "start": 97.559, "end": 97.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.739, "end": 97.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "business", "start": 97.799, "end": 98.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.16, "end": 98.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "with", "start": 98.659, "end": 98.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.799, "end": 98.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "an", "start": 98.819, "end": 98.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.919, "end": 98.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "act", "start": 98.939, "end": 99.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.239, "end": 99.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "elegantly", "start": 99.54, "end": 100.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.059, "end": 100.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "labeled,", "start": 100.099, "end": 100.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.579, "end": 100.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "\"The", "start": 100.579, "end": 101.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.039, "end": 101.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 101.139, "end": 101.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.499, "end": 101.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 101.599, "end": 101.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.679, "end": 101.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 101.72, "end": 101.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.779, "end": 101.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Flame.\"", "start": 101.839, "end": 102.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.359, "end": 102.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "This", "start": 102.819, "end": 102.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.999, "end": 103.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "involved", "start": 103.019, "end": 103.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.379, "end": 103.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 103.399, "end": 103.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.459, "end": 103.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lady", "start": 103.519, "end": 103.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.819, "end": 103.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "wearing", "start": 103.879, "end": 104.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.139, "end": 104.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 104.159, "end": 104.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.219, "end": 104.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "transparent", "start": 104.279, "end": 104.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.919, "end": 104.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "asbestos", "start": 104.979, "end": 105.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 105.739, "end": 105.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "garment", "start": 105.779, "end": 106.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.22, "end": 106.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "under", "start": 106.459, "end": 106.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.639, "end": 106.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 106.639, "end": 106.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.72, "end": 106.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "one", "start": 106.739, "end": 106.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.879, "end": 106.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "you", "start": 106.919, "end": 107.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.019, "end": 107.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "see.", "start": 107.099, "end": 107.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.479, "end": 108.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "The", "start": 108.079, "end": 108.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.18, "end": 108.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gimmick,", "start": 108.18, "end": 108.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.479, "end": 108.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 108.5, "end": 108.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.599, "end": 108.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "course,", "start": 108.639, "end": 108.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.9, "end": 108.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 108.919, "end": 109.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.18, "end": 109.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "that", "start": 109.479, "end": 109.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.619, "end": 109.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "she", "start": 109.639, "end": 109.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.779, "end": 109.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "flitted", "start": 109.86, "end": 110.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.239, "end": 110.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "too", "start": 110.299, "end": 110.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.439, "end": 110.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "close", "start": 110.519, "end": 110.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.759, "end": 110.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 110.819, "end": 110.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.9, "end": 110.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 110.919, "end": 111.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.0, "end": 111.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "flame", "start": 111.059, "end": 111.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.5, "end": 111.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 111.919, "end": 112.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.059, "end": 112.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "her", "start": 112.059, "end": 112.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.18, "end": 112.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "costume", "start": 112.22, "end": 112.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.659, "end": 112.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "got", "start": 112.72, "end": 112.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.879, "end": 112.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "burned", "start": 112.939, "end": 113.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.199, "end": 113.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "off.", "start": 113.239, "end": 113.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.62, "end": 114.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 114.04, "end": 114.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.299, "end": 114.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 114.319, "end": 114.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.419, "end": 114.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "later", "start": 114.459, "end": 114.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.719, "end": 114.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "years,", "start": 114.779, "end": 115.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.099, "end": 115.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "said...", "start": 115.119, "end": 115.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.719, "end": 116.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "I", "start": 116.18, "end": 116.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 116.379, "end": 116.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "burned", "start": 116.419, "end": 116.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 116.699, "end": 116.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "up", "start": 116.699, "end": 116.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 116.939, "end": 117.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "four", "start": 117.019, "end": 117.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 117.379, "end": 117.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "girls", "start": 117.399, "end": 117.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 117.759, "end": 117.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "before", "start": 117.779, "end": 118.04, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.04, "end": 118.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "I", "start": 118.079, "end": 118.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.139, "end": 118.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "got", "start": 118.18, "end": 118.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.339, "end": 118.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "it", "start": 118.339, "end": 118.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.44, "end": 118.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "right.", "start": 118.459, "end": 118.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.879, "end": 119.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Actually,", "start": 119.899, "end": 120.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.439, "end": 120.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "not", "start": 120.479, "end": 120.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.619, "end": 120.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "even", "start": 120.659, "end": 120.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.86, "end": 120.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 120.899, "end": 120.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.979, "end": 121.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "false", "start": 121.119, "end": 121.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.479, "end": 121.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "eyelash", "start": 121.579, "end": 122.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.119, "end": 122.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 122.139, "end": 122.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.36, "end": 122.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "singed.", "start": 122.379, "end": 123.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.039, "end": 123.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 123.959, "end": 124.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.199, "end": 124.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "got", "start": 124.219, "end": 124.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.439, "end": 124.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hot", "start": 124.439, "end": 124.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.659, "end": 124.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 124.659, "end": 124.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.739, "end": 124.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "made", "start": 124.739, "end": 124.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.979, "end": 125.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "money", "start": 125.0, "end": 125.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.219, "end": 125.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "touring", "start": 125.279, "end": 125.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.559, "end": 125.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 125.579, "end": 125.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.659, "end": 125.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "flame", "start": 125.759, "end": 126.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 126.019, "end": 126.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dance", "start": 126.079, "end": 126.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 126.379, "end": 126.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 126.379, "end": 126.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 126.479, "end": 126.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "other", "start": 126.5, "end": 126.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 126.659, "end": 126.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "attractions", "start": 126.68, "end": 127.26, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.26, "end": 127.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "on", "start": 127.279, "end": 127.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.379, "end": 127.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 127.399, "end": 127.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.439, "end": 127.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "road.", "start": 127.519, "end": 127.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.919, "end": 129.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Now", "start": 129.5, "end": 129.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.779, "end": 129.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "with", "start": 129.819, "end": 129.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.939, "end": 129.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 129.94, "end": 129.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.979, "end": 130.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "new", "start": 130.039, "end": 130.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.16, "end": 130.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "press", "start": 130.279, "end": 130.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.539, "end": 130.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "agent,", "start": 130.599, "end": 130.96, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.96, "end": 131.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Bill", "start": 131.0, "end": 131.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.179, "end": 131.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 131.259, "end": 131.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.699, "end": 132.16, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 132.16, "end": 132.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.259, "end": 132.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 132.279, "end": 132.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.419, "end": 132.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ready", "start": 132.479, "end": 132.74, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.74, "end": 132.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 132.759, "end": 132.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.839, "end": 132.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "attack", "start": 132.879, "end": 133.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.22, "end": 133.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Broadway.", "start": 133.3, "end": 133.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.9, "end": 133.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Many,", "start": 133.979, "end": 135.24, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 135.24, "end": 135.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "many", "start": 135.259, "end": 135.46, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 135.46, "end": 135.52, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "years", "start": 135.52, "end": 135.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 135.779, "end": 135.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ago,", "start": 135.819, "end": 136.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 136.299, "end": 136.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "uh,", "start": 136.879, "end": 137.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 137.339, "end": 138.66, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "I", "start": 138.66, "end": 138.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 138.779, "end": 138.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "got", "start": 138.819, "end": 138.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 138.939, "end": 138.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 138.959, "end": 139.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 139.039, "end": 139.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "phone", "start": 139.059, "end": 139.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 139.259, "end": 139.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "call", "start": 139.319, "end": 139.519, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 139.519, "end": 139.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "from", "start": 139.599, "end": 139.839, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 139.839, "end": 140.08, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Mr.", "start": 140.08, "end": 140.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 140.419, "end": 140.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>.", "start": 140.539, "end": 140.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 8.883508443832397, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "eng", "language_probability": 0.9868449568748474, "text": "... came to our neighborhood in Chicago called the Wicker Park area and <PERSON> was a pretty sharp little kid, even at those days. He decided to run the crap games in the schoolyard of the school called Wicker Park. Now, he had to knock, uh, two fellas, or fight two fellas, uh, and matter of fact, they beat him to death but they admired his, uh, his intestinal fortitude so they let him run the games and then he became a partner with these guys. And I was to get 10 cents, I'll be honest with you ladies and gentlemen, to be the lookout. For those that know the gangster jargon, I was to watch out for the cops. Incidentally, I never was paid and if he were alive today, I believe that he would owe me about $140,000 in interest. <PERSON>, as <PERSON><PERSON><PERSON><PERSON> was nicknamed, left school when he was 12 having become the youngest apprentice pharmacist in the history of the State of Illinois. During prohibition, he made a small fortune selling medicinal alcohol. He took a wife when he was barely 17 and still in his teens, he set up a promotional outfit that specialized in high pressure close out sales. When the talkies came in, he moved to California and built sound stages. As a contractor, <PERSON> made and lost a million twice by the time he was 21. Having heard the words spoken on the sound stages, he became convinced he could write. This was when the crash of '29 left him without a construction business. So at the height of the Depression, he returned to Chicago and scratched a living writing jokes and sketches for radio and vaudeville. But it was the Chicago World's Fair of '32 where he made his first big score in show business with an act elegantly labeled, \"The Moth and the Flame.\" This involved a lady wearing a transparent asbestos garment under the one you see. The gimmick, of course, was that she flitted too close to the flame and her costume got burned off. <PERSON>, in later years, said... I burned up four girls before I got it right. Actually, not even a false eyelash was singed. <PERSON> got hot and made money touring the flame dance and other attractions on the road. Now with a new press agent, <PERSON> Dahl, he was ready to attack Broadway. Many, many years ago, uh, I got a phone call from Mr. Todd.", "words": [{"text": "...", "start": 0.079, "end": 0.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 0.319, "end": 1.62, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "came", "start": 1.62, "end": 1.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.779, "end": 1.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 1.799, "end": 1.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 1.919, "end": 1.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "our", "start": 1.919, "end": 2.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.039, "end": 2.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "neighborhood", "start": 2.079, "end": 2.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.339, "end": 2.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 2.339, "end": 2.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.419, "end": 2.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Chicago", "start": 2.44, "end": 2.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.799, "end": 3.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "called", "start": 3.039, "end": 3.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.219, "end": 3.22, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 3.22, "end": 3.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.279, "end": 3.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Wicker", "start": 3.299, "end": 3.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.559, "end": 3.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Park", "start": 3.619, "end": 3.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 3.9, "end": 3.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "area", "start": 3.959, "end": 4.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.279, "end": 5.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 5.019, "end": 5.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.199, "end": 5.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 5.259, "end": 5.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.5, "end": 5.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 5.5, "end": 5.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.639, "end": 5.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 5.679, "end": 5.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.719, "end": 5.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "pretty", "start": 5.779, "end": 6.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.019, "end": 6.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sharp", "start": 6.019, "end": 6.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.279, "end": 6.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "little", "start": 6.279, "end": 6.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.46, "end": 6.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "kid,", "start": 6.5, "end": 6.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.679, "end": 6.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "even", "start": 6.679, "end": 6.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.839, "end": 6.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "at", "start": 6.839, "end": 6.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.94, "end": 6.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "those", "start": 6.96, "end": 7.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.219, "end": 7.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "days.", "start": 7.219, "end": 7.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.659, "end": 8.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "He", "start": 8.559, "end": 8.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 8.719, "end": 8.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "decided", "start": 8.76, "end": 9.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 9.279, "end": 9.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 9.92, "end": 10.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.0, "end": 10.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "run", "start": 10.019, "end": 10.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.139, "end": 10.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 10.159, "end": 10.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.239, "end": 10.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "crap", "start": 10.3, "end": 10.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.539, "end": 10.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "games", "start": 10.559, "end": 10.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.859, "end": 10.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 10.859, "end": 10.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.939, "end": 10.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 10.96, "end": 11.06, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.06, "end": 11.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "schoolyard", "start": 11.219, "end": 11.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.779, "end": 12.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 12.719, "end": 12.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.899, "end": 12.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 12.899, "end": 12.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 12.979, "end": 12.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "school", "start": 12.979, "end": 13.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.239, "end": 13.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "called", "start": 13.259, "end": 13.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.439, "end": 13.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Wicker", "start": 13.439, "end": 13.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 13.699, "end": 13.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Park.", "start": 13.739, "end": 14.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.159, "end": 14.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Now,", "start": 14.719, "end": 14.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.859, "end": 14.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 14.88, "end": 14.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 14.96, "end": 14.96, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "had", "start": 14.96, "end": 15.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.079, "end": 15.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 15.079, "end": 15.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.139, "end": 15.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "knock,", "start": 15.139, "end": 15.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.56, "end": 15.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "uh,", "start": 15.699, "end": 15.72, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 15.72, "end": 16.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "two", "start": 16.299, "end": 16.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.44, "end": 16.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fellas,", "start": 16.539, "end": 16.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 16.959, "end": 17.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "or", "start": 17.54, "end": 17.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.659, "end": 17.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fight", "start": 17.76, "end": 17.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 17.959, "end": 18.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "two", "start": 18.02, "end": 18.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.159, "end": 18.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fellas,", "start": 18.219, "end": 18.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.619, "end": 18.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "uh,", "start": 18.639, "end": 18.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 18.899, "end": 19.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 19.76, "end": 19.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 19.88, "end": 19.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "matter", "start": 19.959, "end": 20.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.099, "end": 20.1, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 20.1, "end": 20.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.159, "end": 20.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fact,", "start": 20.199, "end": 20.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.34, "end": 20.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "they", "start": 20.359, "end": 20.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.5, "end": 20.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "beat", "start": 20.5, "end": 20.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.659, "end": 20.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "him", "start": 20.659, "end": 20.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.779, "end": 20.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 20.779, "end": 20.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 20.899, "end": 20.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "death", "start": 20.939, "end": 21.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.18, "end": 21.26, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "but", "start": 21.26, "end": 21.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.42, "end": 21.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "they", "start": 21.439, "end": 21.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.539, "end": 21.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "admired", "start": 21.539, "end": 21.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 21.959, "end": 21.979, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his,", "start": 21.979, "end": 22.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.279, "end": 22.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "uh,", "start": 22.279, "end": 22.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.439, "end": 22.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "his", "start": 22.439, "end": 22.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 22.579, "end": 22.6, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "intestinal", "start": 22.6, "end": 23.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.04, "end": 23.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fortitude", "start": 23.119, "end": 23.62, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.62, "end": 23.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "so", "start": 23.699, "end": 23.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 23.88, "end": 23.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "they", "start": 23.959, "end": 24.079, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.079, "end": 24.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "let", "start": 24.079, "end": 24.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.199, "end": 24.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "him", "start": 24.199, "end": 24.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.299, "end": 24.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "run", "start": 24.299, "end": 24.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.439, "end": 24.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 24.439, "end": 24.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.54, "end": 24.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "games", "start": 24.559, "end": 24.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.799, "end": 24.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 24.799, "end": 24.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 24.899, "end": 24.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "then", "start": 24.92, "end": 25.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.039, "end": 25.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 25.039, "end": 25.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.159, "end": 25.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "became", "start": 25.159, "end": 25.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.42, "end": 25.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 25.459, "end": 25.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.559, "end": 25.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "partner", "start": 25.559, "end": 25.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 25.859, "end": 25.859, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "with", "start": 25.859, "end": 26.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.02, "end": 26.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "these", "start": 26.039, "end": 26.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.239, "end": 26.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "guys.", "start": 26.299, "end": 26.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.679, "end": 27.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 27.219, "end": 27.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.319, "end": 27.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 27.34, "end": 27.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.42, "end": 27.42, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 27.42, "end": 27.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.559, "end": 27.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 27.579, "end": 27.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.659, "end": 27.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "get", "start": 27.659, "end": 27.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.84, "end": 27.84, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "10", "start": 27.84, "end": 28.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.02, "end": 28.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cents,", "start": 28.079, "end": 28.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.319, "end": 28.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I'll", "start": 28.34, "end": 28.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.459, "end": 28.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "be", "start": 28.459, "end": 28.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.579, "end": 28.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "honest", "start": 28.579, "end": 28.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.799, "end": 28.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "with", "start": 28.819, "end": 28.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.919, "end": 28.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "you", "start": 28.92, "end": 28.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.979, "end": 29.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ladies", "start": 29.0, "end": 29.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.18, "end": 29.18, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 29.18, "end": 29.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.2, "end": 29.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gentlemen,", "start": 29.2, "end": 29.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.42, "end": 29.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 29.439, "end": 29.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.639, "end": 29.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "be", "start": 29.699, "end": 29.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.84, "end": 29.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 29.879, "end": 29.96, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.96, "end": 30.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lookout.", "start": 30.0, "end": 30.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.579, "end": 31.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "For", "start": 31.34, "end": 31.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.46, "end": 31.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "those", "start": 31.479, "end": 31.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.679, "end": 31.679, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 31.679, "end": 31.78, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.78, "end": 31.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "know", "start": 31.799, "end": 31.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.959, "end": 32.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 32.0, "end": 32.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 32.139, "end": 32.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "gangster", "start": 32.599, "end": 33.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.119, "end": 33.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "jargon,", "start": 33.2, "end": 33.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.619, "end": 33.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 33.659, "end": 33.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.84, "end": 34.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 34.159, "end": 34.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.279, "end": 34.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 34.299, "end": 34.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.38, "end": 34.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "watch", "start": 34.419, "end": 34.639, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.639, "end": 34.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "out", "start": 34.659, "end": 34.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.819, "end": 34.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 34.919, "end": 35.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.04, "end": 35.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 35.04, "end": 35.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.119, "end": 35.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "cops.", "start": 35.2, "end": 35.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.54, "end": 35.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Incidentally,", "start": 35.54, "end": 35.959, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 35.959, "end": 35.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 35.959, "end": 36.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.0, "end": 36.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "never", "start": 36.04, "end": 36.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.18, "end": 36.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 36.2, "end": 36.38, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.38, "end": 36.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "paid", "start": 36.439, "end": 36.739, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.739, "end": 36.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 36.779, "end": 36.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.88, "end": 36.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "if", "start": 36.899, "end": 37.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.0, "end": 37.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 37.0, "end": 37.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.119, "end": 37.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "were", "start": 37.119, "end": 37.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.219, "end": 37.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "alive", "start": 37.219, "end": 37.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.459, "end": 37.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "today,", "start": 37.479, "end": 37.859, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.859, "end": 38.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 38.239, "end": 38.339, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.339, "end": 38.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "believe", "start": 38.34, "end": 38.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.559, "end": 38.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 38.559, "end": 38.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.68, "end": 38.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "he", "start": 38.68, "end": 38.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.779, "end": 38.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "would", "start": 38.779, "end": 38.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.899, "end": 38.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "owe", "start": 38.899, "end": 39.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.02, "end": 39.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me", "start": 39.04, "end": 39.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.119, "end": 39.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "about", "start": 39.159, "end": 39.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.299, "end": 39.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "$140,000", "start": 39.299, "end": 40.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.479, "end": 40.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 40.5, "end": 40.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.56, "end": 40.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "interest.", "start": 40.579, "end": 41.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.059, "end": 43.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>,", "start": 43.86, "end": 44.46, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.46, "end": 44.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "as", "start": 44.639, "end": 44.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 44.799, "end": 44.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "start": 44.84, "end": 45.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.199, "end": 45.2, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 45.2, "end": 45.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.34, "end": 45.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nicknamed,", "start": 45.379, "end": 45.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 45.94, "end": 46.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "left", "start": 46.419, "end": 46.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.599, "end": 46.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "school", "start": 46.639, "end": 46.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 46.94, "end": 46.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "when", "start": 46.959, "end": 47.06, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.06, "end": 47.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 47.079, "end": 47.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.139, "end": 47.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 47.159, "end": 47.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.359, "end": 47.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "12", "start": 47.379, "end": 47.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 47.759, "end": 47.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "having", "start": 47.799, "end": 48.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.04, "end": 48.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "become", "start": 48.059, "end": 48.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.319, "end": 48.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 48.36, "end": 48.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.439, "end": 48.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "youngest", "start": 48.5, "end": 48.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 48.86, "end": 48.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "apprentice", "start": 48.879, "end": 49.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 49.459, "end": 49.539, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pharmacist", "start": 49.539, "end": 50.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.119, "end": 50.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 50.139, "end": 50.24, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.24, "end": 50.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 50.259, "end": 50.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.34, "end": 50.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "history", "start": 50.399, "end": 50.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.759, "end": 50.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 50.779, "end": 50.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.84, "end": 50.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 50.86, "end": 50.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 50.919, "end": 50.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "State", "start": 50.979, "end": 51.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.199, "end": 51.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 51.199, "end": 51.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.279, "end": 51.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Illinois.", "start": 51.319, "end": 51.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 51.879, "end": 52.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "During", "start": 52.439, "end": 52.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 52.659, "end": 52.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "prohibition,", "start": 52.719, "end": 53.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 53.419, "end": 53.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 53.899, "end": 54.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.0, "end": 54.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "made", "start": 54.02, "end": 54.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.139, "end": 54.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 54.159, "end": 54.2, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.2, "end": 54.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "small", "start": 54.239, "end": 54.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.5, "end": 54.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fortune", "start": 54.579, "end": 54.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 54.979, "end": 55.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "selling", "start": 55.02, "end": 55.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.319, "end": 55.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "medicinal", "start": 55.34, "end": 55.88, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 55.88, "end": 55.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alcohol.", "start": 55.919, "end": 56.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 56.579, "end": 57.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "He", "start": 57.219, "end": 57.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.359, "end": 57.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "took", "start": 57.36, "end": 57.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.479, "end": 57.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 57.5, "end": 57.56, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.56, "end": 57.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "wife", "start": 57.599, "end": 57.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.819, "end": 57.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "when", "start": 57.819, "end": 57.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.919, "end": 57.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 57.919, "end": 57.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 57.979, "end": 58.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 58.0, "end": 58.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.119, "end": 58.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "barely", "start": 58.119, "end": 58.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 58.4, "end": 58.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "17", "start": 58.439, "end": 59.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.059, "end": 59.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 59.159, "end": 59.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.299, "end": 59.34, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "still", "start": 59.34, "end": 59.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.559, "end": 59.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 59.559, "end": 59.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.639, "end": 59.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "his", "start": 59.639, "end": 59.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 59.799, "end": 59.84, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "teens,", "start": 59.84, "end": 60.28, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.28, "end": 60.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 60.559, "end": 60.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.68, "end": 60.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "set", "start": 60.699, "end": 60.84, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.84, "end": 60.859, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "up", "start": 60.859, "end": 60.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 60.959, "end": 60.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 60.979, "end": 61.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.079, "end": 61.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "promotional", "start": 61.079, "end": 61.56, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.56, "end": 61.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "outfit", "start": 61.599, "end": 61.92, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 61.92, "end": 61.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "that", "start": 61.939, "end": 62.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.079, "end": 62.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "specialized", "start": 62.079, "end": 62.7, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.7, "end": 62.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 62.739, "end": 62.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 62.819, "end": 62.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "high", "start": 62.879, "end": 63.06, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.06, "end": 63.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "pressure", "start": 63.18, "end": 63.52, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.52, "end": 63.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "close", "start": 63.639, "end": 63.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 63.939, "end": 63.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "out", "start": 63.979, "end": 64.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.18, "end": 64.26, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sales.", "start": 64.26, "end": 64.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 64.879, "end": 65.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "When", "start": 65.639, "end": 65.76, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 65.76, "end": 65.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 65.779, "end": 65.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 65.839, "end": 65.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "talkies", "start": 65.879, "end": 66.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.299, "end": 66.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "came", "start": 66.299, "end": 66.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.5, "end": 66.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in,", "start": 66.54, "end": 66.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 66.759, "end": 67.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 67.04, "end": 67.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.159, "end": 67.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "moved", "start": 67.18, "end": 67.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.339, "end": 67.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 67.339, "end": 67.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 67.439, "end": 67.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "California", "start": 67.459, "end": 68.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.04, "end": 68.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 68.059, "end": 68.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.179, "end": 68.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "built", "start": 68.18, "end": 68.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.399, "end": 68.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sound", "start": 68.419, "end": 68.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 68.699, "end": 68.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stages.", "start": 68.739, "end": 69.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 69.44, "end": 69.9, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "As", "start": 69.9, "end": 70.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 70.019, "end": 70.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 70.04, "end": 70.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 70.139, "end": 70.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "contractor,", "start": 70.18, "end": 70.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 70.919, "end": 71.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 71.159, "end": 71.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.459, "end": 71.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "made", "start": 71.519, "end": 71.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 71.919, "end": 71.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 71.919, "end": 72.08, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 72.08, "end": 72.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lost", "start": 72.159, "end": 72.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 72.739, "end": 73.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 73.019, "end": 73.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.119, "end": 73.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "million", "start": 73.199, "end": 73.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 73.599, "end": 73.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "twice", "start": 73.839, "end": 74.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.279, "end": 74.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "by", "start": 74.339, "end": 74.44, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.44, "end": 74.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 74.459, "end": 74.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.559, "end": 74.58, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "time", "start": 74.58, "end": 74.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.739, "end": 74.76, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 74.76, "end": 74.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.839, "end": 74.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 74.86, "end": 74.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 74.979, "end": 75.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "21.", "start": 75.04, "end": 75.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 75.62, "end": 76.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Having", "start": 76.699, "end": 76.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 76.919, "end": 76.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "heard", "start": 76.939, "end": 77.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.139, "end": 77.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 77.159, "end": 77.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.239, "end": 77.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "words", "start": 77.299, "end": 77.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.619, "end": 77.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "spoken", "start": 77.659, "end": 78.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.059, "end": 78.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "on", "start": 78.059, "end": 78.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.159, "end": 78.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 78.18, "end": 78.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.239, "end": 78.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sound", "start": 78.299, "end": 78.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.54, "end": 78.58, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "stages,", "start": 78.58, "end": 79.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.019, "end": 79.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 79.04, "end": 79.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.119, "end": 79.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "became", "start": 79.139, "end": 79.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.419, "end": 79.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "convinced", "start": 79.479, "end": 79.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.919, "end": 79.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 79.939, "end": 80.04, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.04, "end": 80.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "could", "start": 80.059, "end": 80.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.22, "end": 80.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "write.", "start": 80.239, "end": 80.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.68, "end": 81.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "This", "start": 81.5, "end": 81.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.639, "end": 81.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 81.659, "end": 81.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.779, "end": 81.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "when", "start": 81.779, "end": 81.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.899, "end": 81.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 81.919, "end": 81.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.979, "end": 82.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "crash", "start": 82.059, "end": 82.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.319, "end": 82.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 82.339, "end": 82.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.439, "end": 82.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "'29", "start": 82.439, "end": 82.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.979, "end": 83.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "left", "start": 83.019, "end": 83.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.239, "end": 83.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "him", "start": 83.239, "end": 83.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.359, "end": 83.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "without", "start": 83.36, "end": 83.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.599, "end": 83.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 83.639, "end": 83.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.699, "end": 83.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "construction", "start": 83.72, "end": 84.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.239, "end": 84.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "business.", "start": 84.299, "end": 84.8, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.8, "end": 85.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "So", "start": 85.54, "end": 85.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.68, "end": 85.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "at", "start": 85.68, "end": 85.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.779, "end": 85.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 85.799, "end": 85.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.879, "end": 85.9, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "height", "start": 85.9, "end": 86.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.119, "end": 86.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 86.119, "end": 86.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.199, "end": 86.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 86.199, "end": 86.26, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.26, "end": 86.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Depression,", "start": 86.299, "end": 86.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.899, "end": 87.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 87.18, "end": 87.26, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.26, "end": 87.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "returned", "start": 87.279, "end": 87.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.599, "end": 87.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 87.619, "end": 87.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.699, "end": 87.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Chicago", "start": 87.739, "end": 88.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.16, "end": 88.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 88.18, "end": 88.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.279, "end": 88.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "scratched", "start": 88.299, "end": 88.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.72, "end": 88.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 88.759, "end": 88.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.839, "end": 88.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "living", "start": 88.839, "end": 89.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.139, "end": 89.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "writing", "start": 89.159, "end": 89.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.5, "end": 89.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "jokes", "start": 89.559, "end": 89.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.899, "end": 89.9, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 89.9, "end": 89.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.999, "end": 90.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sketches", "start": 90.04, "end": 90.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 90.579, "end": 90.9, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "for", "start": 90.9, "end": 91.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.039, "end": 91.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "radio", "start": 91.059, "end": 91.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.379, "end": 91.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 91.379, "end": 91.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 91.459, "end": 91.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "vaudeville.", "start": 91.519, "end": 92.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 92.059, "end": 93.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "But", "start": 93.119, "end": 93.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.199, "end": 93.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "it", "start": 93.199, "end": 93.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.259, "end": 93.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 93.259, "end": 93.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.379, "end": 93.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 93.379, "end": 93.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.479, "end": 93.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Chicago", "start": 93.519, "end": 93.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.94, "end": 94.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "World's", "start": 94.04, "end": 94.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 94.379, "end": 94.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Fair", "start": 94.459, "end": 94.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 94.739, "end": 94.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 94.739, "end": 94.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 94.839, "end": 94.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "'32", "start": 94.839, "end": 95.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 95.499, "end": 96.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "where", "start": 96.059, "end": 96.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.199, "end": 96.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 96.199, "end": 96.279, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.279, "end": 96.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "made", "start": 96.279, "end": 96.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.399, "end": 96.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "his", "start": 96.419, "end": 96.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.559, "end": 96.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "first", "start": 96.599, "end": 96.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.799, "end": 96.9, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "big", "start": 96.9, "end": 97.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.059, "end": 97.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "score", "start": 97.119, "end": 97.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.399, "end": 97.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 97.419, "end": 97.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.499, "end": 97.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "show", "start": 97.559, "end": 97.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.739, "end": 97.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "business", "start": 97.799, "end": 98.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.16, "end": 98.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "with", "start": 98.659, "end": 98.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.799, "end": 98.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "an", "start": 98.819, "end": 98.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.919, "end": 98.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "act", "start": 98.939, "end": 99.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.239, "end": 99.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "elegantly", "start": 99.54, "end": 100.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.059, "end": 100.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "labeled,", "start": 100.099, "end": 100.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.579, "end": 100.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "\"The", "start": 100.579, "end": 101.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.039, "end": 101.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>", "start": 101.139, "end": 101.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.499, "end": 101.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 101.599, "end": 101.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.679, "end": 101.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 101.72, "end": 101.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.779, "end": 101.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Flame.\"", "start": 101.839, "end": 102.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.359, "end": 102.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "This", "start": 102.819, "end": 102.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.999, "end": 103.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "involved", "start": 103.019, "end": 103.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.379, "end": 103.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 103.399, "end": 103.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.459, "end": 103.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "lady", "start": 103.519, "end": 103.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.819, "end": 103.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "wearing", "start": 103.879, "end": 104.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.139, "end": 104.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 104.159, "end": 104.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.219, "end": 104.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "transparent", "start": 104.279, "end": 104.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.919, "end": 104.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "asbestos", "start": 104.979, "end": 105.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 105.739, "end": 105.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "garment", "start": 105.779, "end": 106.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.22, "end": 106.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "under", "start": 106.459, "end": 106.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.639, "end": 106.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 106.639, "end": 106.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.72, "end": 106.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "one", "start": 106.739, "end": 106.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.879, "end": 106.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "you", "start": 106.919, "end": 107.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.019, "end": 107.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "see.", "start": 107.099, "end": 107.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.479, "end": 108.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "The", "start": 108.079, "end": 108.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.18, "end": 108.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "gimmick,", "start": 108.18, "end": 108.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.479, "end": 108.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 108.5, "end": 108.599, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.599, "end": 108.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "course,", "start": 108.639, "end": 108.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.9, "end": 108.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 108.919, "end": 109.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.18, "end": 109.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "that", "start": 109.479, "end": 109.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.619, "end": 109.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "she", "start": 109.639, "end": 109.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.779, "end": 109.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "flitted", "start": 109.86, "end": 110.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.239, "end": 110.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "too", "start": 110.299, "end": 110.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.439, "end": 110.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "close", "start": 110.519, "end": 110.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.759, "end": 110.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 110.819, "end": 110.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.9, "end": 110.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 110.919, "end": 111.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.0, "end": 111.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "flame", "start": 111.059, "end": 111.5, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.5, "end": 111.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 111.919, "end": 112.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.059, "end": 112.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "her", "start": 112.059, "end": 112.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.18, "end": 112.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "costume", "start": 112.22, "end": 112.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.659, "end": 112.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "got", "start": 112.72, "end": 112.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.879, "end": 112.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "burned", "start": 112.939, "end": 113.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.199, "end": 113.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "off.", "start": 113.239, "end": 113.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.62, "end": 114.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 114.04, "end": 114.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.299, "end": 114.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 114.319, "end": 114.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.419, "end": 114.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "later", "start": 114.459, "end": 114.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.719, "end": 114.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "years,", "start": 114.779, "end": 115.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.099, "end": 115.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "said...", "start": 115.119, "end": 115.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.719, "end": 116.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "I", "start": 116.18, "end": 116.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 116.379, "end": 116.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "burned", "start": 116.419, "end": 116.699, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 116.699, "end": 116.699, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "up", "start": 116.699, "end": 116.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 116.939, "end": 117.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "four", "start": 117.019, "end": 117.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 117.379, "end": 117.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "girls", "start": 117.399, "end": 117.759, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 117.759, "end": 117.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "before", "start": 117.779, "end": 118.04, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.04, "end": 118.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "I", "start": 118.079, "end": 118.139, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.139, "end": 118.18, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "got", "start": 118.18, "end": 118.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.339, "end": 118.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "it", "start": 118.339, "end": 118.44, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.44, "end": 118.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "right.", "start": 118.459, "end": 118.879, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.879, "end": 119.899, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Actually,", "start": 119.899, "end": 120.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.439, "end": 120.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "not", "start": 120.479, "end": 120.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.619, "end": 120.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "even", "start": 120.659, "end": 120.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.86, "end": 120.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 120.899, "end": 120.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.979, "end": 121.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "false", "start": 121.119, "end": 121.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.479, "end": 121.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "eyelash", "start": 121.579, "end": 122.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.119, "end": 122.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 122.139, "end": 122.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.36, "end": 122.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "singed.", "start": 122.379, "end": 123.039, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.039, "end": 123.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 123.959, "end": 124.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.199, "end": 124.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "got", "start": 124.219, "end": 124.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.439, "end": 124.439, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hot", "start": 124.439, "end": 124.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.659, "end": 124.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 124.659, "end": 124.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.739, "end": 124.739, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "made", "start": 124.739, "end": 124.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.979, "end": 125.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "money", "start": 125.0, "end": 125.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.219, "end": 125.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "touring", "start": 125.279, "end": 125.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.559, "end": 125.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 125.579, "end": 125.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.659, "end": 125.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "flame", "start": 125.759, "end": 126.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 126.019, "end": 126.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dance", "start": 126.079, "end": 126.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 126.379, "end": 126.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 126.379, "end": 126.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 126.479, "end": 126.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "other", "start": 126.5, "end": 126.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 126.659, "end": 126.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "attractions", "start": 126.68, "end": 127.26, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.26, "end": 127.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "on", "start": 127.279, "end": 127.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.379, "end": 127.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 127.399, "end": 127.439, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.439, "end": 127.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "road.", "start": 127.519, "end": 127.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.919, "end": 129.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Now", "start": 129.5, "end": 129.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.779, "end": 129.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "with", "start": 129.819, "end": 129.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.939, "end": 129.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 129.94, "end": 129.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.979, "end": 130.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "new", "start": 130.039, "end": 130.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.16, "end": 130.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "press", "start": 130.279, "end": 130.539, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.539, "end": 130.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "agent,", "start": 130.599, "end": 130.96, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.96, "end": 131.0, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Bill", "start": 131.0, "end": 131.179, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.179, "end": 131.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON><PERSON>,", "start": 131.259, "end": 131.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.699, "end": 132.16, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "he", "start": 132.16, "end": 132.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.259, "end": 132.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 132.279, "end": 132.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.419, "end": 132.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ready", "start": 132.479, "end": 132.74, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.74, "end": 132.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 132.759, "end": 132.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.839, "end": 132.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "attack", "start": 132.879, "end": 133.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.22, "end": 133.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Broadway.", "start": 133.3, "end": 133.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.9, "end": 133.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Many,", "start": 133.979, "end": 135.24, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 135.24, "end": 135.259, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "many", "start": 135.259, "end": 135.46, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 135.46, "end": 135.52, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "years", "start": 135.52, "end": 135.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 135.779, "end": 135.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ago,", "start": 135.819, "end": 136.299, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 136.299, "end": 136.879, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "uh,", "start": 136.879, "end": 137.339, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 137.339, "end": 138.66, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "I", "start": 138.66, "end": 138.779, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 138.779, "end": 138.819, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "got", "start": 138.819, "end": 138.939, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 138.939, "end": 138.959, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "a", "start": 138.959, "end": 139.039, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 139.039, "end": 139.059, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "phone", "start": 139.059, "end": 139.259, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 139.259, "end": 139.319, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "call", "start": 139.319, "end": 139.519, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 139.519, "end": 139.599, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "from", "start": 139.599, "end": 139.839, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 139.839, "end": 140.08, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Mr.", "start": 140.08, "end": 140.419, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 140.419, "end": 140.539, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "<PERSON>.", "start": 140.539, "end": 140.999, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}]}}, "created_at": 1754281534.1719296}