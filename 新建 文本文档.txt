2025-08-04 15:38:27,513 - hallucination_fixer - INFO - 音频处理器初始化成功
2025-08-04 15:38:27,518 - hallucination_fixer - INFO - 发现 3 个长条目，开始处理
2025-08-04 15:38:27,518 - hallucination_fixer - INFO - 开始提取 3 个音频片段
2025-08-04 15:38:28,074 - hallucination_fixer - INFO - 成功提取 3 个音频片段
2025-08-04 15:38:28,075 - hallucination_fixer - INFO - 处理条目 #56 (1/3)
[ElevenLabs] 免费模式转录尝试 1/4
[ElevenLabs] 免费模式调用异常 (尝试 1/4): 401 Client Error: Unauthorized for url: https://api.elevenlabs.io/v1/speech-to-text?allow_unauthenticated=1
[ElevenLabs] 免费模式重试第1次，等待2秒...
[ElevenLabs] 免费模式转录尝试 2/4
[ElevenLabs] 免费模式调用异常 (尝试 2/4): 401 Client Error: Unauthorized for url: https://api.elevenlabs.io/v1/speech-to-text?allow_unauthenticated=1
[ElevenLabs] 免费模式重试第2次，等待4秒...
[ElevenLabs] 免费模式转录尝试 3/4
[ElevenLabs] 免费模式调用异常 (尝试 3/4): 401 Client Error: Unauthorized for url: https://api.elevenlabs.io/v1/speech-to-text?allow_unauthenticated=1
[ElevenLabs] 免费模式重试第3次，等待8秒...
[ElevenLabs] 免费模式转录尝试 4/4
[ElevenLabs] 免费模式调用异常 (尝试 4/4): 401 Client Error: Unauthorized for url: https://api.elevenlabs.io/v1/speech-to-text?allow_unauthenticated=1
2025-08-04 15:38:55,288 - hallucination_fixer - ERROR - 转录音频片段失败: 401 Client Error: Unauthorized for url: https://api.elevenlabs.io/v1/speech-to-text?allow_unauthenticated=1
2025-08-04 15:38:55,291 - hallucination_fixer - ERROR - 详细错误信息: Traceback (most recent call last):
  File "E:\github\EvaTrans\hallucination_fix\core\services.py", line 397, in _transcribe_audio_segment
    response = asr_service.transcribe(asr_request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\github\EvaTrans\services\asr\elevenlabs.py", line 121, in transcribe
    result_data = self._transcribe_with_free_mode(request)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\github\EvaTrans\services\asr\elevenlabs.py", line 269, in _transcribe_with_free_mode
    response.raise_for_status()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\requests\models.py", line 1026, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 401 Client Error: Unauthorized for url: https://api.elevenlabs.io/v1/speech-to-text?allow_unauthenticated=1

2025-08-04 15:38:55,292 - hallucination_fixer - WARNING - 条目 #56 转录失败，标记为删除
2025-08-04 15:38:55,292 - hallucination_fixer - INFO - 处理条目 #57 (2/3)
[ElevenLabs] 免费模式转录尝试 1/4
[ElevenLabs] 免费模式调用异常 (尝试 1/4): 401 Client Error: Unauthorized for url: https://api.elevenlabs.io/v1/speech-to-text?allow_unauthenticated=1
[ElevenLabs] 免费模式重试第1次，等待2秒...
[ElevenLabs] 免费模式转录尝试 2/4
[ElevenLabs] 免费模式调用异常 (尝试 2/4): 401 Client Error: Unauthorized for url: https://api.elevenlabs.io/v1/speech-to-text?allow_unauthenticated=1
[ElevenLabs] 免费模式重试第2次，等待4秒...
[ElevenLabs] 免费模式转录尝试 3/4