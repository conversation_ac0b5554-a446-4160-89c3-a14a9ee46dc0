{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754281600", "text": "The money wasn't the important thing. I was shooting for the moon. The show opened October 17 in New York to press and public acclaim. When I watched the audiences, for example, they applauded in the middle of a sequence. The geniuses said that when you're selling out and turning people away from the box office, I shouldn't even change the ushers' uniforms. I went back to the Coast and spent a couple of hundred thousand dollars and fixed it. There's only one genius that I ever recognized, and that's the unconscious geniuses as I call them: the public. You know, a lot of guys grow up and they want to become President of the United States. With me, I just wanted to grow up and marry <PERSON>. And I did. Our marriage ceremony was quiet and simple. But afterwards, <PERSON> arranged a spectacular, <PERSON> doing his nut. I mean, we had firecrackers with \"<PERSON> loves E\" and \"<PERSON> loves M\" all over the sky. All of a sudden, the mountain became alive with exotic dancers doing, um, a fertility dance. The party must have, um, been rather bizarre to some onlookers because here was <PERSON>, uh, 20 years older than his bride, and his bride felt at that time about 90 years old because she was crippled and was in a metal sort of brace, and her groom, well, a 12-year-old in the height of, um, activity. And his young bride was sort of hobbling along in her sort of wheelchair at the age of 24. And people were worried about our age difference. As some of you know, I'm married to a girl who's a few years my junior. As a matter of fact, she's a few years my junior's junior. ", "words": [{"text": "The", "start": 5.559, "end": 5.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.679, "end": 5.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "money", "start": 5.759, "end": 6.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.059, "end": 6.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wasn't", "start": 6.059, "end": 6.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.359, "end": 6.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 6.359, "end": 6.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.44, "end": 6.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "important", "start": 6.5, "end": 6.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.939, "end": 7.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "thing.", "start": 7.0, "end": 7.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.359, "end": 9.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 9.76, "end": 10.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.02, "end": 10.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 10.059, "end": 10.3, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.3, "end": 10.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "shooting", "start": 10.3, "end": 10.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.659, "end": 10.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 10.739, "end": 10.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.84, "end": 10.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 10.88, "end": 10.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.94, "end": 11.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "moon.", "start": 11.019, "end": 11.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.459, "end": 25.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "The", "start": 25.319, "end": 26.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.1, "end": 26.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "show", "start": 26.139, "end": 26.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.42, "end": 26.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "opened", "start": 26.459, "end": 26.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.76, "end": 26.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "October", "start": 26.779, "end": 27.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.279, "end": 27.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "17", "start": 27.359, "end": 27.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.879, "end": 27.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 27.92, "end": 27.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.979, "end": 28.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "New", "start": 28.019, "end": 28.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.139, "end": 28.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "York", "start": 28.159, "end": 28.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.519, "end": 29.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 29.719, "end": 29.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.819, "end": 29.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "press", "start": 29.92, "end": 30.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.319, "end": 30.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 30.439, "end": 30.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.599, "end": 30.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "public", "start": 30.719, "end": 31.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.12, "end": 31.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "acclaim.", "start": 31.179, "end": 31.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.94, "end": 33.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "When", "start": 33.34, "end": 33.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.479, "end": 33.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 33.479, "end": 33.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.599, "end": 33.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "watched", "start": 33.68, "end": 33.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.919, "end": 33.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 33.919, "end": 34.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.0, "end": 34.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "audiences,", "start": 34.04, "end": 34.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.759, "end": 36.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 36.219, "end": 36.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.34, "end": 36.38, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "example,", "start": 36.38, "end": 36.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.94, "end": 37.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "they", "start": 37.439, "end": 37.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.599, "end": 37.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "applauded", "start": 37.599, "end": 38.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.259, "end": 38.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 38.899, "end": 39.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.0, "end": 39.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 39.04, "end": 39.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.119, "end": 39.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "middle", "start": 39.2, "end": 39.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.4, "end": 39.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 39.439, "end": 39.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.52, "end": 39.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 39.54, "end": 39.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.6, "end": 39.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sequence.", "start": 39.639, "end": 40.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.259, "end": 40.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "The", "start": 40.939, "end": 41.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.02, "end": 41.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "geniuses", "start": 41.079, "end": 41.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.599, "end": 41.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "said", "start": 41.599, "end": 41.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.879, "end": 42.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 42.299, "end": 42.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.4, "end": 42.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "when", "start": 42.419, "end": 42.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.559, "end": 42.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "you're", "start": 42.559, "end": 42.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.74, "end": 42.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "selling", "start": 42.759, "end": 43.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.04, "end": 43.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "out", "start": 43.059, "end": 43.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.2, "end": 43.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 43.219, "end": 43.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.319, "end": 43.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "turning", "start": 43.319, "end": 43.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.56, "end": 43.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "people", "start": 43.619, "end": 43.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.86, "end": 43.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "away", "start": 43.879, "end": 44.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.04, "end": 44.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "from", "start": 44.079, "end": 44.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.18, "end": 44.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 44.2, "end": 44.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.279, "end": 44.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "box", "start": 44.319, "end": 44.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.599, "end": 44.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "office,", "start": 44.639, "end": 45.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.019, "end": 45.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 45.52, "end": 45.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.68, "end": 45.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "shouldn't", "start": 45.739, "end": 45.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.979, "end": 46.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "even", "start": 46.0, "end": 46.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.159, "end": 46.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "change", "start": 46.219, "end": 46.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.459, "end": 46.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 46.459, "end": 46.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.56, "end": 46.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ushers'", "start": 46.579, "end": 46.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.9, "end": 46.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "uniforms.", "start": 46.939, "end": 47.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.6, "end": 48.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 48.599, "end": 48.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.699, "end": 48.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "went", "start": 48.759, "end": 48.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.88, "end": 48.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "back", "start": 48.959, "end": 49.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.099, "end": 49.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 49.159, "end": 49.26, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.26, "end": 49.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 49.279, "end": 49.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.34, "end": 49.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Coast", "start": 49.419, "end": 49.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.779, "end": 50.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 50.159, "end": 50.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.259, "end": 50.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spent", "start": 50.279, "end": 50.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.5, "end": 50.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 50.5, "end": 50.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.619, "end": 50.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "couple", "start": 50.659, "end": 50.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.86, "end": 50.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 50.86, "end": 50.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.939, "end": 50.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hundred", "start": 50.959, "end": 51.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.159, "end": 51.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "thousand", "start": 51.259, "end": 51.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.54, "end": 51.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dollars", "start": 51.599, "end": 51.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.88, "end": 51.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 51.899, "end": 52.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.059, "end": 52.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fixed", "start": 52.059, "end": 52.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.319, "end": 52.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it.", "start": 52.34, "end": 52.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.559, "end": 53.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "There's", "start": 53.86, "end": 54.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.02, "end": 54.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "only", "start": 54.039, "end": 54.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.18, "end": 54.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "one", "start": 54.299, "end": 54.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.419, "end": 54.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "genius", "start": 54.5, "end": 54.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.819, "end": 54.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 54.819, "end": 54.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.939, "end": 54.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 54.959, "end": 55.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.059, "end": 55.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ever", "start": 55.139, "end": 55.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.299, "end": 55.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "recognized,", "start": 55.36, "end": 56.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.039, "end": 56.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 56.539, "end": 56.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.599, "end": 56.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that's", "start": 56.599, "end": 56.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.779, "end": 56.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 56.799, "end": 56.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.86, "end": 56.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "unconscious", "start": 56.899, "end": 57.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.459, "end": 57.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "geniuses", "start": 57.52, "end": 57.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.94, "end": 57.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "as", "start": 57.959, "end": 58.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.1, "end": 58.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 58.119, "end": 58.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.219, "end": 58.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "call", "start": 58.259, "end": 58.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.439, "end": 58.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "them:", "start": 58.439, "end": 58.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.679, "end": 59.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 59.459, "end": 59.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.54, "end": 59.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "public.", "start": 59.599, "end": 60.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.1, "end": 62.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "You", "start": 62.699, "end": 62.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.799, "end": 62.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "know,", "start": 62.819, "end": 63.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.04, "end": 64.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 64.299, "end": 64.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.379, "end": 64.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lot", "start": 64.4, "end": 64.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.519, "end": 64.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 64.519, "end": 64.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.599, "end": 64.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "guys", "start": 64.639, "end": 64.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.899, "end": 64.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "grow", "start": 64.939, "end": 65.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.139, "end": 65.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up", "start": 65.199, "end": 65.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.4, "end": 66.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 66.099, "end": 66.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.199, "end": 66.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "they", "start": 66.199, "end": 66.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.299, "end": 66.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "want", "start": 66.299, "end": 66.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.419, "end": 66.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 66.439, "end": 66.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.5, "end": 66.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "become", "start": 66.519, "end": 66.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.759, "end": 66.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "President", "start": 66.86, "end": 67.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.279, "end": 67.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 67.299, "end": 67.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.36, "end": 67.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 67.4, "end": 67.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.459, "end": 67.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "United", "start": 67.479, "end": 67.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.819, "end": 67.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "States.", "start": 67.839, "end": 68.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.32, "end": 69.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "With", "start": 69.119, "end": 69.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.279, "end": 69.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me,", "start": 69.319, "end": 69.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.659, "end": 70.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 70.559, "end": 70.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.699, "end": 70.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "just", "start": 70.779, "end": 70.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.919, "end": 70.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wanted", "start": 70.959, "end": 71.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.12, "end": 71.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 71.139, "end": 71.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.179, "end": 71.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "grow", "start": 71.239, "end": 71.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.379, "end": 71.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up", "start": 71.439, "end": 71.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.54, "end": 71.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 71.559, "end": 71.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.659, "end": 71.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "marry", "start": 71.659, "end": 71.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.919, "end": 71.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 71.939, "end": 72.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.299, "end": 72.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 72.339, "end": 72.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.839, "end": 74.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 74.139, "end": 74.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.239, "end": 74.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 74.239, "end": 74.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.319, "end": 74.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "did.", "start": 74.379, "end": 74.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.719, "end": 77.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Our", "start": 77.259, "end": 77.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.339, "end": 77.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "marriage", "start": 77.379, "end": 77.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.839, "end": 77.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ceremony", "start": 77.86, "end": 78.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.519, "end": 78.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 78.519, "end": 78.76, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.76, "end": 79.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quiet", "start": 79.18, "end": 79.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.759, "end": 80.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 80.479, "end": 80.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.62, "end": 80.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "simple.", "start": 80.659, "end": 81.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.259, "end": 82.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "But", "start": 82.339, "end": 82.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.479, "end": 82.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "afterwards,", "start": 82.519, "end": 83.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.119, "end": 83.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 83.139, "end": 83.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.36, "end": 83.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "arranged", "start": 83.36, "end": 83.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.979, "end": 84.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 84.299, "end": 84.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.379, "end": 84.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "spectacular,", "start": 84.379, "end": 85.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.359, "end": 86.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 86.199, "end": 86.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.459, "end": 86.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 86.5, "end": 86.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.879, "end": 86.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "doing", "start": 86.939, "end": 87.26, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.26, "end": 87.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "his", "start": 87.36, "end": 87.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.619, "end": 87.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nut.", "start": 87.72, "end": 88.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.159, "end": 88.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "I", "start": 88.86, "end": 88.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.94, "end": 88.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mean,", "start": 88.979, "end": 89.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.159, "end": 89.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "we", "start": 89.18, "end": 89.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.34, "end": 89.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "had", "start": 89.36, "end": 89.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.519, "end": 89.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "firecrackers", "start": 89.619, "end": 90.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 90.68, "end": 90.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "with", "start": 90.72, "end": 90.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 90.979, "end": 90.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "\"M", "start": 90.979, "end": 92.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 92.239, "end": 92.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "loves", "start": 92.339, "end": 92.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 92.799, "end": 93.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E\"", "start": 93.299, "end": 93.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.699, "end": 93.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 93.779, "end": 93.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.919, "end": 93.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "\"E", "start": 93.919, "end": 94.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 94.259, "end": 94.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "loves", "start": 94.339, "end": 94.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 94.779, "end": 94.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "M\"", "start": 94.939, "end": 95.32, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 95.32, "end": 95.4, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "all", "start": 95.4, "end": 95.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 95.62, "end": 95.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "over", "start": 95.68, "end": 95.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 95.879, "end": 95.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 95.919, "end": 96.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.0, "end": 96.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sky.", "start": 96.079, "end": 96.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.699, "end": 97.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "All", "start": 97.519, "end": 97.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.659, "end": 97.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 97.68, "end": 97.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.739, "end": 97.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 97.779, "end": 97.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.819, "end": 97.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sudden,", "start": 97.879, "end": 98.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.159, "end": 98.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 98.18, "end": 98.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.259, "end": 98.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mountain", "start": 98.319, "end": 98.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.72, "end": 98.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "became", "start": 98.759, "end": 99.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.099, "end": 99.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alive", "start": 99.099, "end": 99.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.819, "end": 100.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "with", "start": 100.18, "end": 100.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.339, "end": 100.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "exotic", "start": 100.36, "end": 100.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.94, "end": 101.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dancers", "start": 101.04, "end": 101.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.739, "end": 102.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "doing,", "start": 102.519, "end": 102.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.979, "end": 103.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "um,", "start": 103.079, "end": 103.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.419, "end": 103.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 103.779, "end": 103.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.919, "end": 103.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fertility", "start": 103.919, "end": 104.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.579, "end": 104.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dance.", "start": 104.659, "end": 105.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 105.199, "end": 106.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "The", "start": 106.159, "end": 106.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.4, "end": 106.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "party", "start": 106.479, "end": 107.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.0, "end": 107.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "must", "start": 107.419, "end": 107.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.659, "end": 107.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "have,", "start": 107.659, "end": 107.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.959, "end": 107.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "um,", "start": 107.979, "end": 108.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.319, "end": 109.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "been", "start": 109.059, "end": 109.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.259, "end": 109.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "rather", "start": 109.259, "end": 109.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.519, "end": 109.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bizarre", "start": 109.599, "end": 110.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.139, "end": 110.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 110.18, "end": 110.3, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.3, "end": 110.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "some", "start": 110.319, "end": 110.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.579, "end": 110.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "onlookers", "start": 110.68, "end": 111.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.319, "end": 111.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "because", "start": 111.319, "end": 111.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.62, "end": 111.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "here", "start": 111.639, "end": 111.76, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.76, "end": 111.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 111.799, "end": 111.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.94, "end": 111.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 111.979, "end": 112.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.18, "end": 112.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 112.239, "end": 112.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.719, "end": 113.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "uh,", "start": 113.54, "end": 113.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.819, "end": 114.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "20", "start": 114.079, "end": 114.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.379, "end": 114.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "years", "start": 114.399, "end": 114.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.62, "end": 114.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "older", "start": 114.659, "end": 114.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.999, "end": 115.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "than", "start": 115.019, "end": 115.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.18, "end": 115.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "his", "start": 115.199, "end": 115.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.379, "end": 115.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bride,", "start": 115.459, "end": 116.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.079, "end": 116.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 116.54, "end": 116.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.68, "end": 116.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "his", "start": 116.719, "end": 116.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.86, "end": 116.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bride", "start": 116.939, "end": 117.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.359, "end": 117.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "felt", "start": 117.619, "end": 117.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.959, "end": 117.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "at", "start": 117.979, "end": 118.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.099, "end": 118.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "that", "start": 118.119, "end": 118.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.259, "end": 118.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "time", "start": 118.319, "end": 118.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.559, "end": 118.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "about", "start": 118.579, "end": 118.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.86, "end": 118.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "90", "start": 118.919, "end": 119.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.219, "end": 119.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "years", "start": 119.239, "end": 119.48, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.48, "end": 119.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "old", "start": 119.519, "end": 119.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.779, "end": 119.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "because", "start": 119.819, "end": 120.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.059, "end": 120.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "she", "start": 120.079, "end": 120.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.199, "end": 120.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 120.219, "end": 120.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.399, "end": 120.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "crippled", "start": 120.419, "end": 120.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.959, "end": 121.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 121.699, "end": 121.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.999, "end": 122.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 122.059, "end": 122.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.259, "end": 122.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 122.279, "end": 122.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.379, "end": 122.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 122.419, "end": 122.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.54, "end": 122.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "metal", "start": 122.699, "end": 122.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.999, "end": 123.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sort", "start": 123.059, "end": 123.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.219, "end": 123.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 123.239, "end": 123.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.359, "end": 123.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "brace,", "start": 123.399, "end": 123.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.899, "end": 124.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 124.399, "end": 124.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.619, "end": 124.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "her", "start": 124.639, "end": 124.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.779, "end": 124.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "groom,", "start": 124.86, "end": 125.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.419, "end": 126.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "well,", "start": 126.479, "end": 126.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 126.779, "end": 127.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 127.139, "end": 127.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.22, "end": 127.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "12-year-old", "start": 127.279, "end": 128.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.239, "end": 128.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 128.379, "end": 128.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.619, "end": 129.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 129.039, "end": 129.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.16, "end": 129.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "height", "start": 129.22, "end": 129.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.639, "end": 129.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of,", "start": 129.759, "end": 130.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.159, "end": 130.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "um,", "start": 130.239, "end": 130.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.719, "end": 131.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "activity.", "start": 131.039, "end": 131.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.799, "end": 132.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "And", "start": 132.5, "end": 132.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.679, "end": 132.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "his", "start": 132.72, "end": 133.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.02, "end": 133.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "young", "start": 133.059, "end": 133.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.499, "end": 133.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bride", "start": 133.619, "end": 134.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 134.399, "end": 134.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 134.479, "end": 134.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 134.699, "end": 134.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sort", "start": 134.72, "end": 134.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 134.939, "end": 134.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 134.959, "end": 135.1, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 135.1, "end": 135.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hobbling", "start": 135.179, "end": 135.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 135.839, "end": 135.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "along", "start": 135.839, "end": 136.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 136.559, "end": 136.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 136.759, "end": 136.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 136.9, "end": 136.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "her", "start": 136.94, "end": 137.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.099, "end": 137.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sort", "start": 137.119, "end": 137.3, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.3, "end": 137.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 137.3, "end": 137.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.379, "end": 137.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "wheelchair", "start": 137.559, "end": 138.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.639, "end": 138.66, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "at", "start": 138.66, "end": 138.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.819, "end": 138.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 138.86, "end": 138.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.959, "end": 139.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "age", "start": 139.02, "end": 139.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.259, "end": 139.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 139.279, "end": 139.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.419, "end": 139.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "24.", "start": 139.44, "end": 140.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 140.419, "end": 140.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "And", "start": 140.899, "end": 141.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.059, "end": 141.08, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "people", "start": 141.08, "end": 141.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.339, "end": 141.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "were", "start": 141.339, "end": 141.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.54, "end": 141.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "worried", "start": 141.559, "end": 141.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.859, "end": 141.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "about", "start": 141.899, "end": 142.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 142.099, "end": 142.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "our", "start": 142.119, "end": 142.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 142.299, "end": 142.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "age", "start": 142.379, "end": 142.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 142.619, "end": 142.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "difference.", "start": 142.699, "end": 143.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 143.359, "end": 149.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "As", "start": 149.619, "end": 149.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.819, "end": 149.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "some", "start": 149.819, "end": 150.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.0, "end": 150.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 150.02, "end": 150.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.119, "end": 150.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "you", "start": 150.139, "end": 150.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.24, "end": 150.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "know,", "start": 150.279, "end": 150.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.539, "end": 151.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I'm", "start": 151.459, "end": 151.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.58, "end": 151.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "married", "start": 151.599, "end": 151.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.86, "end": 151.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 151.86, "end": 151.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.939, "end": 151.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 151.959, "end": 151.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.999, "end": 152.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "girl", "start": 152.08, "end": 152.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.299, "end": 152.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "who's", "start": 152.319, "end": 152.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.46, "end": 152.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 152.479, "end": 152.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.58, "end": 152.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "few", "start": 152.599, "end": 152.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.759, "end": 152.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "years", "start": 152.8, "end": 153.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.019, "end": 153.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "my", "start": 153.019, "end": 153.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.22, "end": 153.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "junior.", "start": 153.239, "end": 153.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.619, "end": 155.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "As", "start": 155.0, "end": 155.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.139, "end": 155.16, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 155.16, "end": 155.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.22, "end": 155.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "matter", "start": 155.239, "end": 155.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.399, "end": 155.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 155.419, "end": 155.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.519, "end": 155.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fact,", "start": 155.519, "end": 155.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.779, "end": 155.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "she's", "start": 155.779, "end": 155.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.919, "end": 155.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 155.94, "end": 156.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.059, "end": 156.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "few", "start": 156.059, "end": 156.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.199, "end": 156.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "years", "start": 156.239, "end": 156.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.439, "end": 156.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "my", "start": 156.459, "end": 156.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.559, "end": 156.66, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "junior's", "start": 156.66, "end": 156.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.979, "end": 157.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "junior.", "start": 157.039, "end": 157.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.439, "end": 158.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 34.51555848121643, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "eng", "language_probability": 0.983565628528595, "text": "The money wasn't the important thing. I was shooting for the moon. The show opened October 17 in New York to press and public acclaim. When I watched the audiences, for example, they applauded in the middle of a sequence. The geniuses said that when you're selling out and turning people away from the box office, I shouldn't even change the ushers' uniforms. I went back to the Coast and spent a couple of hundred thousand dollars and fixed it. There's only one genius that I ever recognized, and that's the unconscious geniuses as I call them: the public. You know, a lot of guys grow up and they want to become President of the United States. With me, I just wanted to grow up and marry <PERSON>. And I did. Our marriage ceremony was quiet and simple. But afterwards, <PERSON> arranged a spectacular, <PERSON> doing his nut. I mean, we had firecrackers with \"<PERSON> loves E\" and \"<PERSON> loves M\" all over the sky. All of a sudden, the mountain became alive with exotic dancers doing, um, a fertility dance. The party must have, um, been rather bizarre to some onlookers because here was <PERSON>, uh, 20 years older than his bride, and his bride felt at that time about 90 years old because she was crippled and was in a metal sort of brace, and her groom, well, a 12-year-old in the height of, um, activity. And his young bride was sort of hobbling along in her sort of wheelchair at the age of 24. And people were worried about our age difference. As some of you know, I'm married to a girl who's a few years my junior. As a matter of fact, she's a few years my junior's junior. ", "words": [{"text": "The", "start": 5.559, "end": 5.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 5.679, "end": 5.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "money", "start": 5.759, "end": 6.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.059, "end": 6.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wasn't", "start": 6.059, "end": 6.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.359, "end": 6.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 6.359, "end": 6.44, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.44, "end": 6.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "important", "start": 6.5, "end": 6.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 6.939, "end": 7.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "thing.", "start": 7.0, "end": 7.359, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 7.359, "end": 9.76, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 9.76, "end": 10.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.02, "end": 10.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "was", "start": 10.059, "end": 10.3, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.3, "end": 10.3, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "shooting", "start": 10.3, "end": 10.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.659, "end": 10.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 10.739, "end": 10.84, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.84, "end": 10.88, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 10.88, "end": 10.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 10.94, "end": 11.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "moon.", "start": 11.019, "end": 11.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 11.459, "end": 25.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "The", "start": 25.319, "end": 26.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.1, "end": 26.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "show", "start": 26.139, "end": 26.42, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.42, "end": 26.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "opened", "start": 26.459, "end": 26.76, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 26.76, "end": 26.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "October", "start": 26.779, "end": 27.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.279, "end": 27.359, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "17", "start": 27.359, "end": 27.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.879, "end": 27.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 27.92, "end": 27.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 27.979, "end": 28.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "New", "start": 28.019, "end": 28.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.139, "end": 28.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "York", "start": 28.159, "end": 28.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 28.519, "end": 29.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 29.719, "end": 29.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 29.819, "end": 29.92, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "press", "start": 29.92, "end": 30.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.319, "end": 30.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 30.439, "end": 30.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 30.599, "end": 30.719, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "public", "start": 30.719, "end": 31.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.12, "end": 31.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "acclaim.", "start": 31.179, "end": 31.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 31.94, "end": 33.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "When", "start": 33.34, "end": 33.479, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.479, "end": 33.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 33.479, "end": 33.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.599, "end": 33.68, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "watched", "start": 33.68, "end": 33.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 33.919, "end": 33.919, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 33.919, "end": 34.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.0, "end": 34.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "audiences,", "start": 34.04, "end": 34.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 34.759, "end": 36.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 36.219, "end": 36.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.34, "end": 36.38, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "example,", "start": 36.38, "end": 36.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 36.94, "end": 37.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "they", "start": 37.439, "end": 37.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 37.599, "end": 37.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "applauded", "start": 37.599, "end": 38.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 38.259, "end": 38.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "in", "start": 38.899, "end": 39.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.0, "end": 39.04, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 39.04, "end": 39.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.119, "end": 39.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "middle", "start": 39.2, "end": 39.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.4, "end": 39.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 39.439, "end": 39.52, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.52, "end": 39.54, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 39.54, "end": 39.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 39.6, "end": 39.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "sequence.", "start": 39.639, "end": 40.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 40.259, "end": 40.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "The", "start": 40.939, "end": 41.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.02, "end": 41.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "geniuses", "start": 41.079, "end": 41.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.599, "end": 41.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "said", "start": 41.599, "end": 41.879, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 41.879, "end": 42.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 42.299, "end": 42.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.4, "end": 42.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "when", "start": 42.419, "end": 42.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.559, "end": 42.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "you're", "start": 42.559, "end": 42.74, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 42.74, "end": 42.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "selling", "start": 42.759, "end": 43.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.04, "end": 43.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "out", "start": 43.059, "end": 43.2, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.2, "end": 43.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 43.219, "end": 43.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.319, "end": 43.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "turning", "start": 43.319, "end": 43.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.56, "end": 43.619, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "people", "start": 43.619, "end": 43.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 43.86, "end": 43.879, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "away", "start": 43.879, "end": 44.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.04, "end": 44.079, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "from", "start": 44.079, "end": 44.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.18, "end": 44.2, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 44.2, "end": 44.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.279, "end": 44.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "box", "start": 44.319, "end": 44.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 44.599, "end": 44.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "office,", "start": 44.639, "end": 45.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.019, "end": 45.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 45.52, "end": 45.68, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.68, "end": 45.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "shouldn't", "start": 45.739, "end": 45.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 45.979, "end": 46.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "even", "start": 46.0, "end": 46.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.159, "end": 46.219, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "change", "start": 46.219, "end": 46.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.459, "end": 46.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 46.459, "end": 46.56, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.56, "end": 46.579, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ushers'", "start": 46.579, "end": 46.9, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 46.9, "end": 46.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "uniforms.", "start": 46.939, "end": 47.6, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 47.6, "end": 48.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 48.599, "end": 48.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.699, "end": 48.759, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "went", "start": 48.759, "end": 48.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 48.88, "end": 48.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "back", "start": 48.959, "end": 49.099, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.099, "end": 49.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 49.159, "end": 49.26, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.26, "end": 49.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 49.279, "end": 49.34, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.34, "end": 49.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Coast", "start": 49.419, "end": 49.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 49.779, "end": 50.159, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 50.159, "end": 50.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.259, "end": 50.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "spent", "start": 50.279, "end": 50.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.5, "end": 50.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 50.5, "end": 50.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.619, "end": 50.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "couple", "start": 50.659, "end": 50.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.86, "end": 50.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 50.86, "end": 50.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 50.939, "end": 50.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "hundred", "start": 50.959, "end": 51.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.159, "end": 51.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "thousand", "start": 51.259, "end": 51.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.54, "end": 51.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "dollars", "start": 51.599, "end": 51.88, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 51.88, "end": 51.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 51.899, "end": 52.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.059, "end": 52.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fixed", "start": 52.059, "end": 52.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.319, "end": 52.34, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "it.", "start": 52.34, "end": 52.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 52.559, "end": 53.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "There's", "start": 53.86, "end": 54.02, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.02, "end": 54.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "only", "start": 54.039, "end": 54.18, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.18, "end": 54.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "one", "start": 54.299, "end": 54.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.419, "end": 54.5, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "genius", "start": 54.5, "end": 54.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.819, "end": 54.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that", "start": 54.819, "end": 54.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 54.939, "end": 54.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 54.959, "end": 55.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.059, "end": 55.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "ever", "start": 55.139, "end": 55.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 55.299, "end": 55.36, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "recognized,", "start": 55.36, "end": 56.039, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.039, "end": 56.539, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 56.539, "end": 56.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.599, "end": 56.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "that's", "start": 56.599, "end": 56.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.779, "end": 56.799, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 56.799, "end": 56.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 56.86, "end": 56.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "unconscious", "start": 56.899, "end": 57.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.459, "end": 57.52, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "geniuses", "start": 57.52, "end": 57.94, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 57.94, "end": 57.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "as", "start": 57.959, "end": 58.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.1, "end": 58.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 58.119, "end": 58.219, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.219, "end": 58.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "call", "start": 58.259, "end": 58.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.439, "end": 58.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "them:", "start": 58.439, "end": 58.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 58.679, "end": 59.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 59.459, "end": 59.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 59.54, "end": 59.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "public.", "start": 59.599, "end": 60.1, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 60.1, "end": 62.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "You", "start": 62.699, "end": 62.799, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 62.799, "end": 62.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "know,", "start": 62.819, "end": 63.04, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 63.04, "end": 64.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 64.299, "end": 64.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.379, "end": 64.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "lot", "start": 64.4, "end": 64.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.519, "end": 64.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 64.519, "end": 64.599, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.599, "end": 64.639, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "guys", "start": 64.639, "end": 64.899, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 64.899, "end": 64.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "grow", "start": 64.939, "end": 65.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.139, "end": 65.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up", "start": 65.199, "end": 65.4, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 65.4, "end": 66.099, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 66.099, "end": 66.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.199, "end": 66.199, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "they", "start": 66.199, "end": 66.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.299, "end": 66.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "want", "start": 66.299, "end": 66.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.419, "end": 66.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 66.439, "end": 66.5, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.5, "end": 66.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "become", "start": 66.519, "end": 66.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 66.759, "end": 66.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "President", "start": 66.86, "end": 67.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.279, "end": 67.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 67.299, "end": 67.36, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.36, "end": 67.4, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "the", "start": 67.4, "end": 67.459, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.459, "end": 67.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "United", "start": 67.479, "end": 67.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 67.819, "end": 67.839, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "States.", "start": 67.839, "end": 68.32, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 68.32, "end": 69.119, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "With", "start": 69.119, "end": 69.279, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.279, "end": 69.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "me,", "start": 69.319, "end": 69.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 69.659, "end": 70.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 70.559, "end": 70.699, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.699, "end": 70.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "just", "start": 70.779, "end": 70.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 70.919, "end": 70.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "wanted", "start": 70.959, "end": 71.12, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.12, "end": 71.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 71.139, "end": 71.179, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.179, "end": 71.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "grow", "start": 71.239, "end": 71.379, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.379, "end": 71.439, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "up", "start": 71.439, "end": 71.54, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.54, "end": 71.559, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "and", "start": 71.559, "end": 71.659, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.659, "end": 71.659, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "marry", "start": 71.659, "end": 71.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 71.919, "end": 71.939, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>", "start": 71.939, "end": 72.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.299, "end": 72.339, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "<PERSON>.", "start": 72.339, "end": 72.839, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 72.839, "end": 74.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "And", "start": 74.139, "end": 74.239, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.239, "end": 74.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I", "start": 74.239, "end": 74.319, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.319, "end": 74.379, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "did.", "start": 74.379, "end": 74.719, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 74.719, "end": 77.259, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Our", "start": 77.259, "end": 77.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.339, "end": 77.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "marriage", "start": 77.379, "end": 77.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 77.839, "end": 77.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ceremony", "start": 77.86, "end": 78.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.519, "end": 78.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 78.519, "end": 78.76, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 78.76, "end": 79.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "quiet", "start": 79.18, "end": 79.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 79.759, "end": 80.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 80.479, "end": 80.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 80.62, "end": 80.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "simple.", "start": 80.659, "end": 81.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 81.259, "end": 82.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "But", "start": 82.339, "end": 82.479, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 82.479, "end": 82.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "afterwards,", "start": 82.519, "end": 83.119, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.119, "end": 83.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 83.139, "end": 83.36, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.36, "end": 83.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "arranged", "start": 83.36, "end": 83.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 83.979, "end": 84.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 84.299, "end": 84.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 84.379, "end": 84.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "spectacular,", "start": 84.379, "end": 85.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 85.359, "end": 86.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 86.199, "end": 86.459, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.459, "end": 86.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 86.5, "end": 86.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 86.879, "end": 86.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "doing", "start": 86.939, "end": 87.26, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.26, "end": 87.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "his", "start": 87.36, "end": 87.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 87.619, "end": 87.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "nut.", "start": 87.72, "end": 88.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.159, "end": 88.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "I", "start": 88.86, "end": 88.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 88.94, "end": 88.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mean,", "start": 88.979, "end": 89.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.159, "end": 89.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "we", "start": 89.18, "end": 89.34, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.34, "end": 89.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "had", "start": 89.36, "end": 89.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 89.519, "end": 89.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "firecrackers", "start": 89.619, "end": 90.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 90.68, "end": 90.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "with", "start": 90.72, "end": 90.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 90.979, "end": 90.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "\"M", "start": 90.979, "end": 92.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 92.239, "end": 92.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "loves", "start": 92.339, "end": 92.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 92.799, "end": 93.299, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "E\"", "start": 93.299, "end": 93.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.699, "end": 93.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 93.779, "end": 93.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 93.919, "end": 93.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "\"E", "start": 93.919, "end": 94.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 94.259, "end": 94.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "loves", "start": 94.339, "end": 94.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 94.779, "end": 94.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "M\"", "start": 94.939, "end": 95.32, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 95.32, "end": 95.4, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "all", "start": 95.4, "end": 95.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 95.62, "end": 95.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "over", "start": 95.68, "end": 95.879, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 95.879, "end": 95.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 95.919, "end": 96.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.0, "end": 96.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sky.", "start": 96.079, "end": 96.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 96.699, "end": 97.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "All", "start": 97.519, "end": 97.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.659, "end": 97.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 97.68, "end": 97.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.739, "end": 97.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 97.779, "end": 97.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 97.819, "end": 97.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sudden,", "start": 97.879, "end": 98.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.159, "end": 98.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 98.18, "end": 98.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.259, "end": 98.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "mountain", "start": 98.319, "end": 98.72, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 98.72, "end": 98.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "became", "start": 98.759, "end": 99.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.099, "end": 99.099, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "alive", "start": 99.099, "end": 99.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 99.819, "end": 100.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "with", "start": 100.18, "end": 100.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.339, "end": 100.36, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "exotic", "start": 100.36, "end": 100.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 100.94, "end": 101.04, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dancers", "start": 101.04, "end": 101.739, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 101.739, "end": 102.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "doing,", "start": 102.519, "end": 102.979, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 102.979, "end": 103.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "um,", "start": 103.079, "end": 103.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.419, "end": 103.779, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 103.779, "end": 103.919, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 103.919, "end": 103.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "fertility", "start": 103.919, "end": 104.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 104.579, "end": 104.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "dance.", "start": 104.659, "end": 105.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 105.199, "end": 106.159, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "The", "start": 106.159, "end": 106.4, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 106.4, "end": 106.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "party", "start": 106.479, "end": 107.0, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.0, "end": 107.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "must", "start": 107.419, "end": 107.659, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.659, "end": 107.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "have,", "start": 107.659, "end": 107.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 107.959, "end": 107.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "um,", "start": 107.979, "end": 108.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 108.319, "end": 109.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "been", "start": 109.059, "end": 109.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.259, "end": 109.259, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "rather", "start": 109.259, "end": 109.519, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 109.519, "end": 109.599, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bizarre", "start": 109.599, "end": 110.139, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.139, "end": 110.18, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "to", "start": 110.18, "end": 110.3, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.3, "end": 110.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "some", "start": 110.319, "end": 110.579, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 110.579, "end": 110.68, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "onlookers", "start": 110.68, "end": 111.319, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.319, "end": 111.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "because", "start": 111.319, "end": 111.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.62, "end": 111.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "here", "start": 111.639, "end": 111.76, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.76, "end": 111.799, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 111.799, "end": 111.94, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 111.94, "end": 111.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>", "start": 111.979, "end": 112.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.18, "end": 112.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "<PERSON>,", "start": 112.239, "end": 112.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 112.719, "end": 113.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "uh,", "start": 113.54, "end": 113.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 113.819, "end": 114.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "20", "start": 114.079, "end": 114.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.379, "end": 114.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "years", "start": 114.399, "end": 114.62, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.62, "end": 114.659, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "older", "start": 114.659, "end": 114.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 114.999, "end": 115.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "than", "start": 115.019, "end": 115.18, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.18, "end": 115.199, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "his", "start": 115.199, "end": 115.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 115.379, "end": 115.459, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bride,", "start": 115.459, "end": 116.079, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.079, "end": 116.54, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 116.54, "end": 116.68, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.68, "end": 116.719, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "his", "start": 116.719, "end": 116.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 116.86, "end": 116.939, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bride", "start": 116.939, "end": 117.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.359, "end": 117.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "felt", "start": 117.619, "end": 117.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 117.959, "end": 117.979, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "at", "start": 117.979, "end": 118.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.099, "end": 118.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "that", "start": 118.119, "end": 118.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.259, "end": 118.319, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "time", "start": 118.319, "end": 118.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.559, "end": 118.579, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "about", "start": 118.579, "end": 118.86, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 118.86, "end": 118.919, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "90", "start": 118.919, "end": 119.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.219, "end": 119.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "years", "start": 119.239, "end": 119.48, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.48, "end": 119.519, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "old", "start": 119.519, "end": 119.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 119.779, "end": 119.819, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "because", "start": 119.819, "end": 120.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.059, "end": 120.079, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "she", "start": 120.079, "end": 120.199, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.199, "end": 120.219, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 120.219, "end": 120.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.399, "end": 120.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "crippled", "start": 120.419, "end": 120.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 120.959, "end": 121.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 121.699, "end": 121.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 121.999, "end": 122.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 122.059, "end": 122.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.259, "end": 122.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 122.279, "end": 122.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.379, "end": 122.419, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 122.419, "end": 122.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.54, "end": 122.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "metal", "start": 122.699, "end": 122.999, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 122.999, "end": 123.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sort", "start": 123.059, "end": 123.219, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.219, "end": 123.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 123.239, "end": 123.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.359, "end": 123.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "brace,", "start": 123.399, "end": 123.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 123.899, "end": 124.399, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "and", "start": 124.399, "end": 124.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.619, "end": 124.639, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "her", "start": 124.639, "end": 124.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 124.779, "end": 124.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "groom,", "start": 124.86, "end": 125.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 125.419, "end": 126.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "well,", "start": 126.479, "end": 126.779, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 126.779, "end": 127.139, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "a", "start": 127.139, "end": 127.22, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 127.22, "end": 127.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "12-year-old", "start": 127.279, "end": 128.239, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.239, "end": 128.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 128.379, "end": 128.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 128.619, "end": 129.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 129.039, "end": 129.16, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.16, "end": 129.22, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "height", "start": 129.22, "end": 129.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 129.639, "end": 129.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of,", "start": 129.759, "end": 130.159, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.159, "end": 130.239, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "um,", "start": 130.239, "end": 130.719, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 130.719, "end": 131.039, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "activity.", "start": 131.039, "end": 131.799, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 131.799, "end": 132.5, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "And", "start": 132.5, "end": 132.679, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 132.679, "end": 132.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "his", "start": 132.72, "end": 133.02, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.02, "end": 133.059, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "young", "start": 133.059, "end": 133.499, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 133.499, "end": 133.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "bride", "start": 133.619, "end": 134.399, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 134.399, "end": 134.479, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "was", "start": 134.479, "end": 134.699, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 134.699, "end": 134.72, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sort", "start": 134.72, "end": 134.939, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 134.939, "end": 134.959, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 134.959, "end": 135.1, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 135.1, "end": 135.179, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "hobbling", "start": 135.179, "end": 135.839, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 135.839, "end": 135.839, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "along", "start": 135.839, "end": 136.559, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 136.559, "end": 136.759, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "in", "start": 136.759, "end": 136.9, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 136.9, "end": 136.94, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "her", "start": 136.94, "end": 137.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.099, "end": 137.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "sort", "start": 137.119, "end": 137.3, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.3, "end": 137.3, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 137.3, "end": 137.379, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 137.379, "end": 137.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "wheelchair", "start": 137.559, "end": 138.639, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.639, "end": 138.66, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "at", "start": 138.66, "end": 138.819, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.819, "end": 138.86, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "the", "start": 138.86, "end": 138.959, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 138.959, "end": 139.02, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "age", "start": 139.02, "end": 139.259, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.259, "end": 139.279, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "of", "start": 139.279, "end": 139.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 139.419, "end": 139.44, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "24.", "start": 139.44, "end": 140.419, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 140.419, "end": 140.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "And", "start": 140.899, "end": 141.059, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.059, "end": 141.08, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "people", "start": 141.08, "end": 141.339, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.339, "end": 141.339, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "were", "start": 141.339, "end": 141.54, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.54, "end": 141.559, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "worried", "start": 141.559, "end": 141.859, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 141.859, "end": 141.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "about", "start": 141.899, "end": 142.099, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 142.099, "end": 142.119, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "our", "start": 142.119, "end": 142.299, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 142.299, "end": 142.379, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "age", "start": 142.379, "end": 142.619, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 142.619, "end": 142.699, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "difference.", "start": 142.699, "end": 143.359, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 143.359, "end": 149.619, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "As", "start": 149.619, "end": 149.819, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 149.819, "end": 149.819, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "some", "start": 149.819, "end": 150.0, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.0, "end": 150.02, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 150.02, "end": 150.119, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.119, "end": 150.139, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "you", "start": 150.139, "end": 150.24, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.24, "end": 150.279, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "know,", "start": 150.279, "end": 150.539, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 150.539, "end": 151.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "I'm", "start": 151.459, "end": 151.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.58, "end": 151.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "married", "start": 151.599, "end": 151.86, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.86, "end": 151.86, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 151.86, "end": 151.939, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.939, "end": 151.959, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 151.959, "end": 151.999, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 151.999, "end": 152.08, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "girl", "start": 152.08, "end": 152.299, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.299, "end": 152.319, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "who's", "start": 152.319, "end": 152.46, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.46, "end": 152.479, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 152.479, "end": 152.58, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.58, "end": 152.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "few", "start": 152.599, "end": 152.759, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 152.759, "end": 152.8, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "years", "start": 152.8, "end": 153.019, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.019, "end": 153.019, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "my", "start": 153.019, "end": 153.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.22, "end": 153.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "junior.", "start": 153.239, "end": 153.619, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 153.619, "end": 155.0, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "As", "start": 155.0, "end": 155.139, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.139, "end": 155.16, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 155.16, "end": 155.22, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.22, "end": 155.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "matter", "start": 155.239, "end": 155.399, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.399, "end": 155.419, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "of", "start": 155.419, "end": 155.519, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.519, "end": 155.519, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "fact,", "start": 155.519, "end": 155.779, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.779, "end": 155.779, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "she's", "start": 155.779, "end": 155.919, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 155.919, "end": 155.94, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "a", "start": 155.94, "end": 156.059, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.059, "end": 156.059, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "few", "start": 156.059, "end": 156.199, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.199, "end": 156.239, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "years", "start": 156.239, "end": 156.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.439, "end": 156.459, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "my", "start": 156.459, "end": 156.559, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.559, "end": 156.66, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "junior's", "start": 156.66, "end": 156.979, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 156.979, "end": 157.039, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "junior.", "start": 157.039, "end": 157.439, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 157.439, "end": 158.699, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}]}}, "created_at": 1754281635.2756467}