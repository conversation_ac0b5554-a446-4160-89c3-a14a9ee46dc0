{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754281709", "text": "Good night. Thanks for listening to us.", "words": [{"text": "Good", "start": 2.259, "end": 2.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.419, "end": 2.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "night.", "start": 2.44, "end": 2.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.8, "end": 3.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Thanks", "start": 3.899, "end": 4.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.159, "end": 4.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 4.179, "end": 4.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.259, "end": 4.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "listening", "start": 4.299, "end": 4.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.579, "end": 4.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 4.599, "end": 4.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.679, "end": 4.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "us.", "start": 4.739, "end": 5.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 1.3672962188720703, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "eng", "language_probability": 0.9756617546081543, "text": "Good night. Thanks for listening to us.", "words": [{"text": "Good", "start": 2.259, "end": 2.419, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.419, "end": 2.44, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "night.", "start": 2.44, "end": 2.8, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 2.8, "end": 3.899, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "Thanks", "start": 3.899, "end": 4.159, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.159, "end": 4.179, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "for", "start": 4.179, "end": 4.259, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.259, "end": 4.299, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "listening", "start": 4.299, "end": 4.579, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.579, "end": 4.599, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "to", "start": 4.599, "end": 4.679, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": " ", "start": 4.679, "end": 4.739, "type": "spacing", "speaker_id": "speaker_0", "logprob": 0.0}, {"text": "us.", "start": 4.739, "end": 5.08, "type": "word", "speaker_id": "speaker_0", "logprob": 0.0}]}}, "created_at": 1754281710.6321664}