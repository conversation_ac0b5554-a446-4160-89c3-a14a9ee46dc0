{"success": true, "service": "elevenlabs", "transcription_id": "elevenlabs_1754307515", "text": "Ой, ой, ой! Ой-ой-ой! А now мы видим старую душевнулою бабулю. Как она теперь будет за собой следить? Няня такая: Я тебе, ну я тебя сейчас задам! Слушай здесь, сюда подтянись. Только не то. Это же мои деньги. Что ты делаешь? Я говорила тебе... Ну, это все равно, что если бы я взяла и поехала с тобым туда вот так, без слов даже, понял? Так давайте я вам скажу еще раз все по порядку, чтобы вы потом могли подумать и переосмыслить это. Так что же делать? Ну что же мы делаем? Мы заходим в комнату и пытаемся убедиться самому в том, что бабушка или дедуля живы. А пока мы этого не сделаем, мы их просто беспокоили. Начинаем им позвонять. Да кто ж там стучится в такое время? Мы запросто можем представить себе эту картинку. Стоп! Стоп! Кто-то на пороге? Кто там такой? Что он хочет? А может быть, его заметил сосед или родственник? Пожалуйста, посторониеся! Это очень важно для расследования. В этом случае нужно было обратиться к нам сразу после того, как вы услышали звук Сейчас приедут полицейские, они разберутся. Здесь есть камеры видео наблюдения, должны поймать всех участников этого восстания. В том числе и тех, кто стрелял из газового баллончика. По городу Пороховую лужицу. Быть, пожалуйста, аккуратнее. Вы нас не слышите? Нас не слышите?! Да нет, конечно. Разве можно так делать? Больные люди. Приходят на прием. Они сидели дома на карантине два месяца. Они ждали нас. Мы должны им помочь. Актер театра и кино Никита Данченко рассказывает о роли врача-нарколога в сериале «Сестрички». В ролях он также играет главного героя Сергея Трофимова из фильма «Голодные игры» и других известных персонажей. У меня была знакомая девушка, у нее детка умерла от героина. Она бросается ей помогать, ее тоже забирают в наркологию. И потом эти женщины становятся такими закованными, замкнутыми, их сложно освободить. Как только тело перестает болеть, начинается другое - оно начинает скованность чувствовать, потерянный слух, одышку. Люди приходят к врачу с жалобами на головные боли или боли в спине, но уже с нарушениями координат мозга, которые могут привести к параличу. В таком случае уже бессильна ни одна из современных методик реабилитации. В сериале также снимаются Ольга Копуненко и Кирилл Серебренников. Они играют родителей Сергея Трофимова - врача-нарколога. Меня зовут Виктория Полякова. Мне 43 года. Я прожила в Санкт-Петербурге всю свою жизнь: тридцать восемь лет живу в Петербурге, и пять лет живу здесь в Москве. Я закончила ГИТИС, где была на факультете кинематографии и телевидения. Также я занимаюсь в Театральной лаборатории у Клаудиа Ларина мастерскойой. В свободное от работы время мне нравится готовить дома или вообще куда-нибудь поехать, просто посидеть с друзьями и поговорить о том, что нас сейчас очень всех беспокоит в России и на планете Земля вообще. Съемки сериала проходили осенью 2020 года. На тот момент я была беременна вторым ребенком. Я снималась там в роли женщины-прокурора, которая занимается расследованием этого дела о гибели моего мужа от рук наркоторговца. Это было очень тяжело для меня. У нас сын маленький был тогда еще ребенок. А потом у него начались проблемы со здоровьем. Мы обратились к врачам, которые сказали нам, что надо лечить нервы. И мы решили, что нужно просто отдохнуть душой и телом. Нужно поехать куда-нибудь в теплые края.", "words": [{"text": "<PERSON><PERSON>,", "start": 1.74, "end": 1.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 1.759, "end": 1.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ой,", "start": 1.879, "end": 1.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 1.899, "end": 1.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ой!", "start": 1.899, "end": 1.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 1.899, "end": 1.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ой-ой-ой!", "start": 1.899, "end": 2.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "А", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "now", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "мы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "видим", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "старую", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "душевнулою", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "бабулю.", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Как", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "она", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "теперь", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "будет", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "за", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "собой", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "следить?", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Няня", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "такая:", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Я", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "тебе,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ну", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "я", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "тебя", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "сейчас", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "задам!", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Слушай", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "здесь,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "сюда", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "подтянись.", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Только", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "не", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "то.", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Это", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "же", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "мои", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "деньги.", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Что", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ты", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "делаешь?", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Я", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "говорила", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "тебе...", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "Ну,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "это", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "все", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "равно,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "что", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "если", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "бы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "я", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "взяла", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "и", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "поехала", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "с", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "тобым", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "туда", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "вот", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "так,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "без", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "слов", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "даже,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "понял?", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "Так", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "давайте", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "я", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "вам", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "скажу", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "еще", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "раз", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "все", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "по", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "порядку,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "чтобы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "вы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "потом", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "могли", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "подумать", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "и", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "переосмыслить", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "это.", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "Так", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "что", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "же", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "делать?", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Ну", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "что", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "же", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "мы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "делаем?", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Мы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "заходим", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "в", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "комнату", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "и", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "пытаемся", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "убедиться", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "самому", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "в", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "том,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "что", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "бабушка", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "или", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "дедуля", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "живы.", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "А", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "пока", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "мы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "этого", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "не", "start": 2.019, "end": 2.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.099, "end": 2.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "сделаем,", "start": 2.099, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "мы", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "их", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "просто", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "беспокоили.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Начинаем", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "им", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "позвонять.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Да", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "кто", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "ж", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "там", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "стучится", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "в", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "такое", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "время?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Мы", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "запросто", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "можем", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "представить", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "себе", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "эту", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "картин<PERSON><PERSON>.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Стоп!", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_10", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_10", "logprob": 0.0}, {"text": "Стоп!", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_10", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_10", "logprob": 0.0}, {"text": "Кто-то", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "на", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "пороге?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "Кто", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "там", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "такой?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "Что", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "он", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "хочет?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "А", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "может", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "быть,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "его", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "заметил", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "сосед", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "или", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "родственник?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "Пожалуйста,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "посторониеся!", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "Это", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "очень", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "важно", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "для", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "расследования.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "В", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "этом", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "случае", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "нужно", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "было", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "обратиться", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "к", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "нам", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "сразу", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "после", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "того,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "как", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "вы", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "услышали", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "звук", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "Сей<PERSON><PERSON>с", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "приедут", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "полицейские,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "они", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "разберутся.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "Здесь", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "есть", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "камеры", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "видео", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "наблюдения,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "должны", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "поймать", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "всех", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "уча<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "этого", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "восстания.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "В", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "том", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "числе", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "и", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "тех,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "кто", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "стрелял", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "из", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "газового", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "баллончика.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "По", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "городу", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "Пороховую", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "лужицу.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "Быть,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "пожалуйста,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "аккуратнее.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Вы", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "нас", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "не", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "слышите?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Нас", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "не", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "слышите?!", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Да", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "нет,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "конечно.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Разве", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "можно", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "так", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "делать?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Больные", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "люди.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Приходят", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "на", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "прием.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "сидели", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "дома", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "на", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "карантине", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "два", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "месяца.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "ж<PERSON>а<PERSON>и", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "нас.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Мы", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "должны", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "им", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "помочь.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "театра", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "и", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "кино", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "Никита", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "Данченко", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "рассказывает", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "о", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "роли", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "врача-нарколога", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "в", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "сериале", "start": 2.119, "end": 4.239, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 4.239, "end": 4.239, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "«Сестрички».", "start": 4.239, "end": 5.139, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.139, "end": 5.139, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "В", "start": 5.139, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "ролях", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "он", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "также", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "играет", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "главного", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "героя", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "Сергея", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "Трофимова", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "из", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "фильма", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "«Голодные", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "игры»", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "и", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "других", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "известных", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "персо<PERSON><PERSON><PERSON><PERSON>.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "У", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "меня", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "была", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "знакомая", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "девушка,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "у", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "нее", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "детка", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "умерла", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "от", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "героина.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "Она", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "бросается", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "ей", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "помогать,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "ее", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "тоже", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "заб<PERSON>р<PERSON><PERSON>т", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "в", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "наркологию.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "И", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "потом", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "эти", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "женщины", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "становятся", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "такими", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "закованными,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "замкнутыми,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "их", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "сложно", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "освободить.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "Как", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "только", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "тело", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "перестает", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "болеть,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "начинается", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "другое", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "-", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "оно", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "начинает", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "скованность", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "чувствовать,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "потерянный", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "слух,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "одышку.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "Люди", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "приходят", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "к", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "врачу", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "с", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "жалобами", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "на", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "головные", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "боли", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "или", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "боли", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "в", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "спине,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "но", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "уже", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "с", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "нарушениями", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "координат", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "мозга,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "которые", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "могут", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "привести", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "к", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "парали<PERSON>у.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "В", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "таком", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "случае", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "уже", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "бессильна", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "ни", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "одна", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "из", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "современных", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "методик", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "реабилитации.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "В", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "сериале", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "также", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "снимаются", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ольга", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Копуненко", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "и", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>ер<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "start": 5.159, "end": 5.179, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.179, "end": 5.219, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 5.219, "end": 6.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 6.159, "end": 6.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "играют", "start": 6.159, "end": 40.72, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 40.72, "end": 41.36, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "родителей", "start": 41.36, "end": 42.9, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 42.9, "end": 42.9, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Сергея", "start": 42.9, "end": 51.5, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 51.5, "end": 51.5, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Трофимова", "start": 51.5, "end": 55.059, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 55.059, "end": 55.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "-", "start": 55.059, "end": 55.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 55.079, "end": 55.199, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "врача-нарколога.", "start": 55.199, "end": 68.319, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 68.319, "end": 68.439, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Меня", "start": 68.439, "end": 69.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 69.099, "end": 69.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "зовут", "start": 69.099, "end": 70.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 70.119, "end": 70.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Виктория", "start": 70.159, "end": 73.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 73.399, "end": 73.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Полякова.", "start": 73.399, "end": 77.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 77.639, "end": 77.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Мне", "start": 77.759, "end": 77.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 77.899, "end": 78.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "43", "start": 78.039, "end": 78.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 78.619, "end": 78.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "года.", "start": 78.619, "end": 78.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 78.959, "end": 78.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Я", "start": 78.959, "end": 79.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.119, "end": 79.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "прожила", "start": 79.179, "end": 79.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.279, "end": 79.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "в", "start": 79.279, "end": 79.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.279, "end": 79.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Санкт-Петербурге", "start": 79.279, "end": 79.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.659, "end": 79.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "всю", "start": 79.659, "end": 79.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.659, "end": 79.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "свою", "start": 79.659, "end": 79.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.679, "end": 79.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "жизнь:", "start": 79.679, "end": 79.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.719, "end": 79.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "тридцать", "start": 79.719, "end": 79.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.999, "end": 80.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "восемь", "start": 80.119, "end": 80.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.259, "end": 80.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "лет", "start": 80.259, "end": 80.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.259, "end": 80.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "живу", "start": 80.259, "end": 80.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.319, "end": 80.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "в", "start": 80.319, "end": 80.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.319, "end": 80.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Петербурге,", "start": 80.319, "end": 80.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.659, "end": 81.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "и", "start": 81.379, "end": 81.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.399, "end": 81.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "пять", "start": 81.759, "end": 81.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.779, "end": 81.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "лет", "start": 81.779, "end": 81.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.779, "end": 81.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "живу", "start": 81.779, "end": 82.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 82.219, "end": 82.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "здесь", "start": 82.239, "end": 82.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 82.259, "end": 82.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "в", "start": 82.259, "end": 82.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 82.439, "end": 82.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Москве.", "start": 82.439, "end": 82.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 82.659, "end": 82.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Я", "start": 82.839, "end": 83.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.019, "end": 83.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "закончила", "start": 83.219, "end": 83.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.239, "end": 83.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ГИТИС,", "start": 83.239, "end": 83.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.239, "end": 83.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "где", "start": 83.239, "end": 83.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.239, "end": 83.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "была", "start": 83.239, "end": 83.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.239, "end": 83.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "на", "start": 83.319, "end": 83.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.379, "end": 83.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "факультете", "start": 83.459, "end": 84.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.099, "end": 85.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "кинематографии", "start": 85.059, "end": 85.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.199, "end": 85.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "и", "start": 85.199, "end": 85.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.199, "end": 85.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "телевидения.", "start": 85.499, "end": 86.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.399, "end": 86.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Так<PERSON>е", "start": 86.519, "end": 86.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.779, "end": 88.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "я", "start": 88.099, "end": 88.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.119, "end": 88.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "занимаюсь", "start": 88.139, "end": 88.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.219, "end": 88.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "в", "start": 88.219, "end": 88.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.219, "end": 88.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Театральной", "start": 88.219, "end": 88.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.499, "end": 88.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "лаборатории", "start": 88.599, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "у", "start": 88.739, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Клаудиа", "start": 88.739, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ларина", "start": 88.739, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "мастерскойой.", "start": 88.739, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "В", "start": 88.739, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "свободное", "start": 88.739, "end": 88.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.959, "end": 88.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "от", "start": 88.959, "end": 88.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.979, "end": 88.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "работы", "start": 88.979, "end": 88.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.999, "end": 88.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "время", "start": 88.999, "end": 88.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.999, "end": 88.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "мне", "start": 88.999, "end": 88.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.999, "end": 88.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "нравится", "start": 88.999, "end": 90.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 90.659, "end": 90.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "готовить", "start": 90.739, "end": 100.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 100.439, "end": 100.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "дома", "start": 100.539, "end": 104.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 104.599, "end": 104.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "или", "start": 104.599, "end": 118.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.319, "end": 118.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "вообще", "start": 118.439, "end": 118.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.459, "end": 118.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "куда-нибудь", "start": 118.459, "end": 142.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 142.079, "end": 142.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "поехать,", "start": 142.079, "end": 160.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.259, "end": 161.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "просто", "start": 161.219, "end": 163.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.419, "end": 163.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "посидеть", "start": 163.419, "end": 163.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.619, "end": 163.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "с", "start": 163.619, "end": 163.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.619, "end": 163.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "друзьями", "start": 163.619, "end": 223.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 223.519, "end": 223.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "и", "start": 223.519, "end": 223.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 223.999, "end": 223.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "поговорить", "start": 223.999, "end": 230.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.439, "end": 230.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "о", "start": 230.439, "end": 230.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.439, "end": 230.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "том,", "start": 230.439, "end": 237.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 237.499, "end": 237.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "что", "start": 237.499, "end": 237.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 237.779, "end": 251.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "нас", "start": 251.639, "end": 251.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 251.679, "end": 251.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "сейчас", "start": 251.679, "end": 252.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.319, "end": 252.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "очень", "start": 252.439, "end": 256.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 256.619, "end": 256.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "всех", "start": 256.619, "end": 258.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.099, "end": 258.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "беспокоит", "start": 258.099, "end": 258.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.799, "end": 258.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "в", "start": 258.919, "end": 258.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.939, "end": 258.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "России", "start": 258.939, "end": 258.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.939, "end": 258.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "и", "start": 258.939, "end": 258.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.939, "end": 259.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "на", "start": 259.739, "end": 259.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 259.939, "end": 260.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "планете", "start": 260.059, "end": 271.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 271.339, "end": 271.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Земля", "start": 271.999, "end": 274.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 274.339, "end": 274.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "вообще.", "start": 274.339, "end": 274.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 274.579, "end": 274.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Съемки", "start": 274.579, "end": 274.839, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 274.839, "end": 274.839, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "сериала", "start": 274.839, "end": 277.039, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 277.039, "end": 277.039, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "проходили", "start": 277.039, "end": 279.639, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 279.639, "end": 279.799, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "осенью", "start": 279.799, "end": 280.479, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.479, "end": 280.739, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "2020", "start": 280.739, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "года.", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "На", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "тот", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "момент", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "я", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "была", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "беременна", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "вторым", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "ребенком.", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Я", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "снималась", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "там", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "в", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "роли", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "женщины-прокурора,", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "которая", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "занимается", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "расследованием", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "этого", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "дела", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "о", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "гибели", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "моего", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "мужа", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "от", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "рук", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "наркоторговца.", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Это", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "было", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "очень", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "тяжело", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "для", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "меня.", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "У", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "нас", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "сын", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "маленький", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "был", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "тогда", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "еще", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "ребенок.", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "А", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "потом", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "у", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "него", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "начались", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "проблемы", "start": 280.759, "end": 280.799, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.799, "end": 280.799, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "со", "start": 280.799, "end": 281.079, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 281.079, "end": 281.219, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "здоровьем.", "start": 281.219, "end": 283.319, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 283.319, "end": 283.379, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Мы", "start": 283.379, "end": 283.439, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 283.439, "end": 283.559, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "обратились", "start": 283.559, "end": 285.339, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.339, "end": 285.339, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "к", "start": 285.339, "end": 285.359, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.359, "end": 285.359, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "врачам,", "start": 285.359, "end": 285.359, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.359, "end": 285.359, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "которые", "start": 285.359, "end": 285.359, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.359, "end": 285.359, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "сказали", "start": 285.359, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "нам,", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "что", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "надо", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "лечить", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "нервы.", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "И", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "мы", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "решили,", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "что", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "нужно", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "просто", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "отдохнуть", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "ду<PERSON><PERSON><PERSON>", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.519, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "и", "start": 285.519, "end": 285.539, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.539, "end": 285.539, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "телом.", "start": 285.539, "end": 285.539, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.539, "end": 285.539, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Нужно", "start": 285.539, "end": 285.539, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.539, "end": 285.539, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "поехать", "start": 285.539, "end": 285.819, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.819, "end": 285.819, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "куда-нибудь", "start": 285.819, "end": 285.819, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.819, "end": 285.819, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "в", "start": 285.819, "end": 285.819, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.819, "end": 285.819, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "теплые", "start": 285.819, "end": 285.819, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.819, "end": 285.819, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "края.", "start": 285.819, "end": 285.919, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}], "language_detected": null, "confidence": null, "processing_time": 80.82308197021484, "metadata": {"service": "elevenlabs", "mode": "free", "model": "scribe_v1", "raw_result": {"language_code": "rus", "language_probability": 1.0, "text": "Ой, ой, ой! Ой-ой-ой! А now мы видим старую душевнулою бабулю. Как она теперь будет за собой следить? Няня такая: Я тебе, ну я тебя сейчас задам! Слушай здесь, сюда подтянись. Только не то. Это же мои деньги. Что ты делаешь? Я говорила тебе... Ну, это все равно, что если бы я взяла и поехала с тобым туда вот так, без слов даже, понял? Так давайте я вам скажу еще раз все по порядку, чтобы вы потом могли подумать и переосмыслить это. Так что же делать? Ну что же мы делаем? Мы заходим в комнату и пытаемся убедиться самому в том, что бабушка или дедуля живы. А пока мы этого не сделаем, мы их просто беспокоили. Начинаем им позвонять. Да кто ж там стучится в такое время? Мы запросто можем представить себе эту картинку. Стоп! Стоп! Кто-то на пороге? Кто там такой? Что он хочет? А может быть, его заметил сосед или родственник? Пожалуйста, посторониеся! Это очень важно для расследования. В этом случае нужно было обратиться к нам сразу после того, как вы услышали звук Сейчас приедут полицейские, они разберутся. Здесь есть камеры видео наблюдения, должны поймать всех участников этого восстания. В том числе и тех, кто стрелял из газового баллончика. По городу Пороховую лужицу. Быть, пожалуйста, аккуратнее. Вы нас не слышите? Нас не слышите?! Да нет, конечно. Разве можно так делать? Больные люди. Приходят на прием. Они сидели дома на карантине два месяца. Они ждали нас. Мы должны им помочь. Актер театра и кино Никита Данченко рассказывает о роли врача-нарколога в сериале «Сестрички». В ролях он также играет главного героя Сергея Трофимова из фильма «Голодные игры» и других известных персонажей. У меня была знакомая девушка, у нее детка умерла от героина. Она бросается ей помогать, ее тоже забирают в наркологию. И потом эти женщины становятся такими закованными, замкнутыми, их сложно освободить. Как только тело перестает болеть, начинается другое - оно начинает скованность чувствовать, потерянный слух, одышку. Люди приходят к врачу с жалобами на головные боли или боли в спине, но уже с нарушениями координат мозга, которые могут привести к параличу. В таком случае уже бессильна ни одна из современных методик реабилитации. В сериале также снимаются Ольга Копуненко и Кирилл Серебренников. Они играют родителей Сергея Трофимова - врача-нарколога. Меня зовут Виктория Полякова. Мне 43 года. Я прожила в Санкт-Петербурге всю свою жизнь: тридцать восемь лет живу в Петербурге, и пять лет живу здесь в Москве. Я закончила ГИТИС, где была на факультете кинематографии и телевидения. Также я занимаюсь в Театральной лаборатории у Клаудиа Ларина мастерскойой. В свободное от работы время мне нравится готовить дома или вообще куда-нибудь поехать, просто посидеть с друзьями и поговорить о том, что нас сейчас очень всех беспокоит в России и на планете Земля вообще. Съемки сериала проходили осенью 2020 года. На тот момент я была беременна вторым ребенком. Я снималась там в роли женщины-прокурора, которая занимается расследованием этого дела о гибели моего мужа от рук наркоторговца. Это было очень тяжело для меня. У нас сын маленький был тогда еще ребенок. А потом у него начались проблемы со здоровьем. Мы обратились к врачам, которые сказали нам, что надо лечить нервы. И мы решили, что нужно просто отдохнуть душой и телом. Нужно поехать куда-нибудь в теплые края.", "words": [{"text": "<PERSON><PERSON>,", "start": 1.74, "end": 1.759, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 1.759, "end": 1.879, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ой,", "start": 1.879, "end": 1.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 1.899, "end": 1.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "ой!", "start": 1.899, "end": 1.899, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 1.899, "end": 1.899, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "Ой-ой-ой!", "start": 1.899, "end": 2.019, "type": "word", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_1", "logprob": 0.0}, {"text": "А", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "now", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "мы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "видим", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "старую", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "душевнулою", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "бабулю.", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Как", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "она", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "теперь", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "будет", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "за", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "собой", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "следить?", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Няня", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "такая:", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Я", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "тебе,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "ну", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "я", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "тебя", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "сейчас", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "задам!", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Слушай", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "здесь,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "сюда", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "подтянись.", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Только", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "не", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "то.", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Это", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "же", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "мои", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "деньги.", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_5", "logprob": 0.0}, {"text": "Что", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "ты", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "делаешь?", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_6", "logprob": 0.0}, {"text": "Я", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "говорила", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "тебе...", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "Ну,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "это", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "все", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "равно,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "что", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "если", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "бы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "я", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "взяла", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "и", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "поехала", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "с", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "тобым", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "туда", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "вот", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "так,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "без", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "слов", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "даже,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "понял?", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_7", "logprob": 0.0}, {"text": "Так", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "давайте", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "я", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "вам", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "скажу", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "еще", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "раз", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "все", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "по", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "порядку,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "чтобы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "вы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "потом", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "могли", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "подумать", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "и", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "переосмыслить", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "это.", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "Так", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "что", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "же", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "делать?", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Ну", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "что", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "же", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "мы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "делаем?", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Мы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "заходим", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "в", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "комнату", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "и", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "пытаемся", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "убедиться", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "самому", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "в", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "том,", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "что", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "бабушка", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "или", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "дедуля", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "живы.", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "А", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "пока", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "мы", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "этого", "start": 2.019, "end": 2.019, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.019, "end": 2.019, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "не", "start": 2.019, "end": 2.099, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.099, "end": 2.099, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "сделаем,", "start": 2.099, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "мы", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "их", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "просто", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "беспокоили.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Начинаем", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "им", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "позвонять.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_3", "logprob": 0.0}, {"text": "Да", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "кто", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "ж", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "там", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "стучится", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "в", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "такое", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "время?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Мы", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "запросто", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "можем", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "представить", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "себе", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "эту", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "картин<PERSON><PERSON>.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Стоп!", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_10", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_10", "logprob": 0.0}, {"text": "Стоп!", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_10", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_10", "logprob": 0.0}, {"text": "Кто-то", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "на", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "пороге?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "Кто", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "там", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "такой?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "Что", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "он", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "хочет?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "А", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "может", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "быть,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "его", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "заметил", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "сосед", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "или", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "родственник?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_11", "logprob": 0.0}, {"text": "Пожалуйста,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "посторониеся!", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "Это", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "очень", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "важно", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "для", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "расследования.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_12", "logprob": 0.0}, {"text": "В", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "этом", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "случае", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "нужно", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "было", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "обратиться", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "к", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "нам", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "сразу", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "после", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "того,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "как", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "вы", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "услышали", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "звук", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_13", "logprob": 0.0}, {"text": "Сей<PERSON><PERSON>с", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "приедут", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "полицейские,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "они", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "разберутся.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "Здесь", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "есть", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "камеры", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "видео", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "наблюдения,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "должны", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "поймать", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "всех", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "уча<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "этого", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "восстания.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "В", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "том", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "числе", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "и", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "тех,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "кто", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "стрелял", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "из", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "газового", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "баллончика.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "По", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "городу", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "Пороховую", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "лужицу.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_14", "logprob": 0.0}, {"text": "Быть,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "пожалуйста,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "аккуратнее.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Вы", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "нас", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "не", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "слышите?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Нас", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "не", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "слышите?!", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Да", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "нет,", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "конечно.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Разве", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "можно", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "так", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "делать?", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Больные", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "люди.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Приходят", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "на", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "прием.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "сидели", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "дома", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "на", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "карантине", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "два", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "месяца.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "ж<PERSON>а<PERSON>и", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "нас.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "Мы", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "должны", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "им", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "помочь.", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_15", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "театра", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "и", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "кино", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "Никита", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "Данченко", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "рассказывает", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "о", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "роли", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "врача-нарколога", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "в", "start": 2.119, "end": 2.119, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 2.119, "end": 2.119, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "сериале", "start": 2.119, "end": 4.239, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 4.239, "end": 4.239, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "«Сестрички».", "start": 4.239, "end": 5.139, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.139, "end": 5.139, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "В", "start": 5.139, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "ролях", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "он", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "также", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "играет", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "главного", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "героя", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "Сергея", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "Трофимова", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "из", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "фильма", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "«Голодные", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "игры»", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "и", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "других", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "известных", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "персо<PERSON><PERSON><PERSON><PERSON>.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_8", "logprob": 0.0}, {"text": "У", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "меня", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "была", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "знакомая", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "девушка,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "у", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "нее", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "детка", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "умерла", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "от", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "героина.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "Она", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "бросается", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "ей", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "помогать,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "ее", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "тоже", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "заб<PERSON>р<PERSON><PERSON>т", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "в", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "наркологию.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "И", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "потом", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "эти", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "женщины", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "становятся", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "такими", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "закованными,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "замкнутыми,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "их", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "сложно", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "освободить.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_16", "logprob": 0.0}, {"text": "Как", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "только", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "тело", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "перестает", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "болеть,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "начинается", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "другое", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "-", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "оно", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "начинает", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "скованность", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "чувствовать,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "потерянный", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "слух,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "одышку.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "Люди", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "приходят", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "к", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "врачу", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "с", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "жалобами", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "на", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "головные", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "боли", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "или", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "боли", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "в", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "спине,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "но", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "уже", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "с", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "нарушениями", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "координат", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "мозга,", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "которые", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "могут", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "привести", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "к", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "парали<PERSON>у.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "В", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "таком", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "случае", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "уже", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "бессильна", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "ни", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "одна", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "из", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "современных", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "методик", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "реабилитации.", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_17", "logprob": 0.0}, {"text": "В", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "сериале", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "также", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "снимаются", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Ольга", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Копуненко", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "и", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start": 5.159, "end": 5.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.159, "end": 5.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON>ер<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "start": 5.159, "end": 5.179, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 5.179, "end": 5.219, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "<PERSON><PERSON><PERSON>", "start": 5.219, "end": 6.159, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 6.159, "end": 6.159, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "играют", "start": 6.159, "end": 40.72, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 40.72, "end": 41.36, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "родителей", "start": 41.36, "end": 42.9, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 42.9, "end": 42.9, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Сергея", "start": 42.9, "end": 51.5, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 51.5, "end": 51.5, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Трофимова", "start": 51.5, "end": 55.059, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 55.059, "end": 55.059, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "-", "start": 55.059, "end": 55.079, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 55.079, "end": 55.199, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "врача-нарколога.", "start": 55.199, "end": 68.319, "type": "word", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": " ", "start": 68.319, "end": 68.439, "type": "spacing", "speaker_id": "speaker_4", "logprob": 0.0}, {"text": "Меня", "start": 68.439, "end": 69.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 69.099, "end": 69.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "зовут", "start": 69.099, "end": 70.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 70.119, "end": 70.159, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Виктория", "start": 70.159, "end": 73.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 73.399, "end": 73.399, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Полякова.", "start": 73.399, "end": 77.639, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 77.639, "end": 77.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Мне", "start": 77.759, "end": 77.899, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 77.899, "end": 78.039, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "43", "start": 78.039, "end": 78.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 78.619, "end": 78.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "года.", "start": 78.619, "end": 78.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 78.959, "end": 78.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Я", "start": 78.959, "end": 79.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.119, "end": 79.179, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "прожила", "start": 79.179, "end": 79.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.279, "end": 79.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "в", "start": 79.279, "end": 79.279, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.279, "end": 79.279, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Санкт-Петербурге", "start": 79.279, "end": 79.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.659, "end": 79.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "всю", "start": 79.659, "end": 79.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.659, "end": 79.659, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "свою", "start": 79.659, "end": 79.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.679, "end": 79.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "жизнь:", "start": 79.679, "end": 79.719, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.719, "end": 79.719, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "тридцать", "start": 79.719, "end": 79.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 79.999, "end": 80.119, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "восемь", "start": 80.119, "end": 80.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.259, "end": 80.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "лет", "start": 80.259, "end": 80.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.259, "end": 80.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "живу", "start": 80.259, "end": 80.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.319, "end": 80.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "в", "start": 80.319, "end": 80.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.319, "end": 80.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Петербурге,", "start": 80.319, "end": 80.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 80.659, "end": 81.379, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "и", "start": 81.379, "end": 81.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.399, "end": 81.759, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "пять", "start": 81.759, "end": 81.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.779, "end": 81.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "лет", "start": 81.779, "end": 81.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 81.779, "end": 81.779, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "живу", "start": 81.779, "end": 82.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 82.219, "end": 82.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "здесь", "start": 82.239, "end": 82.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 82.259, "end": 82.259, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "в", "start": 82.259, "end": 82.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 82.439, "end": 82.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Москве.", "start": 82.439, "end": 82.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 82.659, "end": 82.839, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Я", "start": 82.839, "end": 83.019, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.019, "end": 83.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "закончила", "start": 83.219, "end": 83.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.239, "end": 83.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "ГИТИС,", "start": 83.239, "end": 83.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.239, "end": 83.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "где", "start": 83.239, "end": 83.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.239, "end": 83.239, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "была", "start": 83.239, "end": 83.239, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.239, "end": 83.319, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "на", "start": 83.319, "end": 83.379, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 83.379, "end": 83.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "факультете", "start": 83.459, "end": 84.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 84.099, "end": 85.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "кинематографии", "start": 85.059, "end": 85.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.199, "end": 85.199, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "и", "start": 85.199, "end": 85.199, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 85.199, "end": 85.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "телевидения.", "start": 85.499, "end": 86.399, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.399, "end": 86.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Так<PERSON>е", "start": 86.519, "end": 86.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 86.779, "end": 88.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "я", "start": 88.099, "end": 88.119, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.119, "end": 88.139, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "занимаюсь", "start": 88.139, "end": 88.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.219, "end": 88.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "в", "start": 88.219, "end": 88.219, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.219, "end": 88.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Театральной", "start": 88.219, "end": 88.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.499, "end": 88.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "лаборатории", "start": 88.599, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "у", "start": 88.739, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Клаудиа", "start": 88.739, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Ларина", "start": 88.739, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "мастерскойой.", "start": 88.739, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "В", "start": 88.739, "end": 88.739, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.739, "end": 88.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "свободное", "start": 88.739, "end": 88.959, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.959, "end": 88.959, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "от", "start": 88.959, "end": 88.979, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.979, "end": 88.979, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "работы", "start": 88.979, "end": 88.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.999, "end": 88.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "время", "start": 88.999, "end": 88.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.999, "end": 88.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "мне", "start": 88.999, "end": 88.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 88.999, "end": 88.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "нравится", "start": 88.999, "end": 90.659, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 90.659, "end": 90.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "готовить", "start": 90.739, "end": 100.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 100.439, "end": 100.539, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "дома", "start": 100.539, "end": 104.599, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 104.599, "end": 104.599, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "или", "start": 104.599, "end": 118.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.319, "end": 118.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "вообще", "start": 118.439, "end": 118.459, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 118.459, "end": 118.459, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "куда-нибудь", "start": 118.459, "end": 142.079, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 142.079, "end": 142.079, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "поехать,", "start": 142.079, "end": 160.259, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 160.259, "end": 161.219, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "просто", "start": 161.219, "end": 163.419, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.419, "end": 163.419, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "посидеть", "start": 163.419, "end": 163.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.619, "end": 163.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "с", "start": 163.619, "end": 163.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 163.619, "end": 163.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "друзьями", "start": 163.619, "end": 223.519, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 223.519, "end": 223.519, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "и", "start": 223.519, "end": 223.999, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 223.999, "end": 223.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "поговорить", "start": 223.999, "end": 230.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.439, "end": 230.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "о", "start": 230.439, "end": 230.439, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 230.439, "end": 230.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "том,", "start": 230.439, "end": 237.499, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 237.499, "end": 237.499, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "что", "start": 237.499, "end": 237.779, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 237.779, "end": 251.639, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "нас", "start": 251.639, "end": 251.679, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 251.679, "end": 251.679, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "сейчас", "start": 251.679, "end": 252.319, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 252.319, "end": 252.439, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "очень", "start": 252.439, "end": 256.619, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 256.619, "end": 256.619, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "всех", "start": 256.619, "end": 258.099, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.099, "end": 258.099, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "беспокоит", "start": 258.099, "end": 258.799, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.799, "end": 258.919, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "в", "start": 258.919, "end": 258.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.939, "end": 258.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "России", "start": 258.939, "end": 258.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.939, "end": 258.939, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "и", "start": 258.939, "end": 258.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 258.939, "end": 259.739, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "на", "start": 259.739, "end": 259.939, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 259.939, "end": 260.059, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "планете", "start": 260.059, "end": 271.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 271.339, "end": 271.999, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Земля", "start": 271.999, "end": 274.339, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 274.339, "end": 274.339, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "вообще.", "start": 274.339, "end": 274.579, "type": "word", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": " ", "start": 274.579, "end": 274.579, "type": "spacing", "speaker_id": "speaker_2", "logprob": 0.0}, {"text": "Съемки", "start": 274.579, "end": 274.839, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 274.839, "end": 274.839, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "сериала", "start": 274.839, "end": 277.039, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 277.039, "end": 277.039, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "проходили", "start": 277.039, "end": 279.639, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 279.639, "end": 279.799, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "осенью", "start": 279.799, "end": 280.479, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.479, "end": 280.739, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "2020", "start": 280.739, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "года.", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "На", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "тот", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "момент", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "я", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "была", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "беременна", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "вторым", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "ребенком.", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Я", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "снималась", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "там", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "в", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "роли", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "женщины-прокурора,", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "которая", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "занимается", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "расследованием", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "этого", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "дела", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "о", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "гибели", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "моего", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "мужа", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "от", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "рук", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "наркоторговца.", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Это", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "было", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "очень", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "тяжело", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "для", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "меня.", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "У", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "нас", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "сын", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "маленький", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "был", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "тогда", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "еще", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "ребенок.", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "А", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "потом", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "у", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "него", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "начались", "start": 280.759, "end": 280.759, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.759, "end": 280.759, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "проблемы", "start": 280.759, "end": 280.799, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 280.799, "end": 280.799, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "со", "start": 280.799, "end": 281.079, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 281.079, "end": 281.219, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "здоровьем.", "start": 281.219, "end": 283.319, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 283.319, "end": 283.379, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Мы", "start": 283.379, "end": 283.439, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 283.439, "end": 283.559, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "обратились", "start": 283.559, "end": 285.339, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.339, "end": 285.339, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "к", "start": 285.339, "end": 285.359, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.359, "end": 285.359, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "врачам,", "start": 285.359, "end": 285.359, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.359, "end": 285.359, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "которые", "start": 285.359, "end": 285.359, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.359, "end": 285.359, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "сказали", "start": 285.359, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "нам,", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "что", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "надо", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "лечить", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "нервы.", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "И", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "мы", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "решили,", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "что", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "нужно", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "просто", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "отдохнуть", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.499, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "ду<PERSON><PERSON><PERSON>", "start": 285.499, "end": 285.499, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.499, "end": 285.519, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "и", "start": 285.519, "end": 285.539, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.539, "end": 285.539, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "телом.", "start": 285.539, "end": 285.539, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.539, "end": 285.539, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "Нужно", "start": 285.539, "end": 285.539, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.539, "end": 285.539, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "поехать", "start": 285.539, "end": 285.819, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.819, "end": 285.819, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "куда-нибудь", "start": 285.819, "end": 285.819, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.819, "end": 285.819, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "в", "start": 285.819, "end": 285.819, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.819, "end": 285.819, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "теплые", "start": 285.819, "end": 285.819, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": " ", "start": 285.819, "end": 285.819, "type": "spacing", "speaker_id": "speaker_9", "logprob": 0.0}, {"text": "края.", "start": 285.819, "end": 285.919, "type": "word", "speaker_id": "speaker_9", "logprob": 0.0}]}}, "created_at": 1754307596.6121738}